import 'dart:convert';
import 'dart:io';
import 'dart:async';
import 'dart:math' as math;
import 'package:path_provider/path_provider.dart';
import 'package:get/get.dart';
import 'package:novel_app/models/novel.dart';
import 'package:novel_app/models/novel_outline.dart';
import 'package:novel_app/prompts/genre_prompts.dart';
import 'package:novel_app/models/character_type.dart';
import 'package:novel_app/models/character_card.dart';
import 'package:novel_app/models/generation_mode.dart';
import 'package:novel_app/services/cache_service.dart';
import 'package:novel_app/services/export_service.dart';
import 'package:novel_app/services/character_type_service.dart';
import 'package:novel_app/services/character_card_service.dart';
import 'package:novel_app/services/ai_service.dart';
import 'package:novel_app/services/daily_limit_service.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:novel_app/models/writing_style_package.dart';
import 'package:novel_app/langchain/services/novel_generation_service.dart';
import 'package:novel_app/langchain/services/lightweight_generation_service.dart';
import 'package:novel_app/controllers/api_config_controller.dart';
import 'package:novel_app/langchain/models/novel_memory.dart';
import 'package:novel_app/services/novel_vectorization_service.dart';
import 'package:novel_app/services/enhanced_outline_import_service.dart';
import 'package:novel_app/controllers/knowledge_base_controller.dart';
import 'package:novel_app/screens/short_novel_world_building_preview_screen.dart';
import 'package:novel_app/screens/short_novel_outline_preview_screen.dart';
import 'package:flutter/material.dart';

// 新增：小说生成状态枚举
enum NovelGenerationStatus {
  notStarted, // 未开始
  generating, // 生成中
  paused, // 已暂停
  interrupted, // 中断（有部分章节但未完成）
  completed, // 已完成
}

// Add Enum at the top or in a separate file
enum GenerationStage {
  idle, // 初始状态
  generatingOutline, // 正在生成大纲
  outlineReady, // 大纲已生成，等待用户确认/修改
  generatingDetailedOutline, // 正在生成细纲
  detailedOutlineReady, // 细纲已生成，等待用户确认
  generatingChapters, // 正在生成章节
  generationComplete, // 全部完成
  error, // 发生错误

  // 短篇小说专用阶段
  generatingShortNovelWorldBuilding, // 正在生成短篇小说世界观
  shortNovelWorldBuildingReady, // 短篇小说世界观已生成
  generatingShortNovelOutline, // 正在生成短篇小说详细大纲
  shortNovelOutlineReady, // 短篇小说详细大纲已生成
  generatingShortNovelContent, // 正在生成短篇小说内容
}

class NovelController extends GetxController {
  final _cacheService = Get.find<CacheService>();
  final _exportService = ExportService();
  final _characterTypeService = Get.find<CharacterTypeService>();
  final _characterCardService = Get.find<CharacterCardService>();
  final _aiService = Get.find<AIService>();

  final novels = <Novel>[].obs;
  final title = ''.obs;
  final background = ''.obs;
  final otherRequirements = ''.obs;
  final style = '轻松幽默'.obs;
  final selectedGenres = <String>[].obs;
  final theme = ''.obs; // 主题内容
  final targetReaders = '男性向'.obs; // 目标读者
  final specialRequirements = <String>[].obs; // 特殊要求

  // 添加目标读者变量
  final targetReader = '男性向'.obs;

  // 新增角色选择相关的变量
  final selectedCharacterTypes = <CharacterType>[].obs;
  final RxMap<String, List<CharacterCard>> selectedCharacterCards =
      <String, List<CharacterCard>>{}.obs; // 支持多选
  final selectedWritingStyle = Rx<WritingStylePackage?>(null); // 写作风格包

  // 短篇小说相关变量
  final novelType = NovelType.longNovel.obs;
  final shortNovelWordCount = ShortNovelWordCount.words10000.obs;
  final Rx<ShortNovelOutline?> currentShortNovelOutline =
      Rx<ShortNovelOutline?>(null);
  final shortNovelGeneratedContent = ''.obs;
  final shortNovelCurrentPart = 0.obs;
  final shortNovelWorldBuilding = ''.obs; // 世界观和角色发展脉络
  final shortNovelDetailedOutline = ''.obs; // 详细大纲

  final isGenerating = false.obs;
  final generationStatus = ''.obs;
  final generationProgress = 0.0.obs;
  final realtimeOutput = ''.obs;
  final isPaused = false.obs;
  final _shouldStop = false.obs;
  final _currentChapter = 0.obs; // 添加当前章节记录
  final _hasOutline = false.obs; // 添加大纲状态记录
  final _currentSessionId = ''.obs; // 当前会话ID

  // 新增：暂停控制相关
  final _pauseCompleter = Rxn<Completer<void>>();
  final _generationStartTime = Rxn<DateTime>();
  final _lastSaveTime = Rxn<DateTime>();

  // 新增：生成状态跟踪
  final _targetTotalChapters = 0.obs; // 目标总章节数
  final _completedChapters = 0.obs; // 已完成章节数
  final isGenerationComplete = false.obs; // 是否生成完成
  final isParametersRestored = false.obs; // 参数是否已从保存状态恢复

  static const String _novelsBoxName = 'novels';
  static const String _chaptersBoxName = 'generated_chapters';
  late final Box<dynamic> _novelsBox;
  late final Box<dynamic> _chaptersBox;
  final RxList<Chapter> _generatedChapters = <Chapter>[].obs;

  List<Chapter> get generatedChapters => _generatedChapters;

  // 添加大纲相关变量
  final currentOutline = Rx<NovelOutline?>(null);
  final rawGeneratedOutlineText = ''.obs;
  final isUsingOutline = false.obs;

  // 新增属性
  final RxString _currentNovelBackground = ''.obs;
  final RxList<String> _specialRequirements = <String>[].obs;
  final RxString _selectedStyle = ''.obs;
  final RxInt _totalChapters = 5.obs;
  final RxString _currentNovelTitle = ''.obs;

  // 会话ID管理
  final Map<String, String> _novelSessionIds = <String, String>{};

  // 新增getter
  String get currentNovelBackground => _currentNovelBackground.value;
  String get selectedStyle => _selectedStyle.value;
  int get totalChapters => _totalChapters.value;
  RxInt get totalChaptersRx => _totalChapters;
  String get currentNovelTitle => _currentNovelTitle.value;
  String get currentSessionId => _currentSessionId.value;

  // 新增setter方法
  void setNovelTitle(String title) {
    _currentNovelTitle.value = title;
  }

  void setNovelBackground(String background) {
    _currentNovelBackground.value = background;
  }

  void setSpecialRequirements(List<String> requirements) {
    _specialRequirements.assignAll(requirements);
  }

  void setSelectedGenres(List<String> genres) {
    selectedGenres.assignAll(genres);
  }

  void setSelectedCharacterTypes(List<CharacterType> types) {
    selectedCharacterTypes.assignAll(types);
  }

  void setSelectedCharacterCards(Map<String, CharacterCard> cards) {
    selectedCharacterCards.clear();
    cards.forEach((typeId, card) {
      selectedCharacterCards[typeId] = [card];
    });
    selectedCharacterCards.refresh();
  }

  void setSelectedCharacterCardsList(Map<String, List<CharacterCard>> cards) {
    selectedCharacterCards.assignAll(cards);
  }

  void setSelectedStyle(String style) {
    _selectedStyle.value = style;
  }

  void setTargetReader(String value) => targetReader.value = value;

  void setTotalChapters(int value) {
    if (value > 0) {
      // 如果用户输入的值超过1000，给出提示但仍然允许设置
      if (value > 1000) {
        Get.snackbar(
          '提示',
          '章节数量较多，生成时间可能会较长，建议不要超过1000章',
          duration: const Duration(seconds: 5),
        );
      }
      _totalChapters.value = value;
    } else {
      Get.snackbar('错误', '章节数量必须大于0');
      _totalChapters.value = 1; // 设置为最小值
    }
  }

  void setUsingOutline(bool useOutline) {
    isUsingOutline.value = useOutline;
  }

  void selectWritingStyle(WritingStylePackage? package) {
    selectedWritingStyle.value = package;
  }

  String getWritingStylePrompt() {
    if (selectedWritingStyle.value == null) {
      return '';
    }

    final package = selectedWritingStyle.value!;
    return '''
请参考以下文风示例进行创作：
作者：${package.author}
风格描述：${package.description}
示例文本：
${package.sampleTexts.join('\n')}

请严格按照上述文风进行创作，保持相同的写作风格、语言特点和叙事方式。
''';
  }

  // 添加LangChain服务
  final NovelGenerationService _langchainService =
      Get.find<NovelGenerationService>();

  // 添加精简生成服务
  final LightweightGenerationService _lightweightService =
      Get.find<LightweightGenerationService>();

  // 添加生成模式
  final Rx<GenerationMode> generationMode = GenerationMode.lightweight.obs;

  // Add generation stage state
  final Rx<GenerationStage> generationStage = GenerationStage.idle.obs;

  @override
  void onInit() async {
    super.onInit();
    await _initHive();
    await loadNovels(); // 加载保存的小说
    await _checkAndRestoreGenerationStates(); // 检查并恢复生成状态
  }

  // 切换生成模式
  void toggleGenerationMode(dynamic mode) {
    // 如果传入的是布尔值，根据布尔值设置模式
    if (mode is bool) {
      generationMode.value =
          mode ? GenerationMode.lightweight : GenerationMode.standard;
    }
    // 如果传入的是GenerationMode枚举，直接设置
    else if (mode is GenerationMode) {
      generationMode.value = mode;
    }

    Get.snackbar(
      '已切换生成模式',
      '当前模式: ${generationMode.value.displayName}\n${generationMode.value.description}',
      duration: const Duration(seconds: 3),
    );
  }

  Future<void> _initHive() async {
    _novelsBox = await Hive.openBox(_novelsBoxName);
    _chaptersBox = await Hive.openBox(_chaptersBoxName);
    _loadGeneratedChapters();
  }

  Future<void> _saveToHive(Novel novel) async {
    try {
      // 确保小说标题不为空
      if (novel.title.isEmpty) {
        throw Exception('小说标题不能为空');
      }

      // 规范化小说标题作为键
      final String safeTitle = novel.title.trim();
      final novelKey = 'novel_$safeTitle';

      print('[DEBUG] 保存小说到Hive，键: $novelKey');
      print('[DEBUG] 小说内容长度: ${novel.content.length}字');
      print('[DEBUG] 小说大纲长度: ${novel.outline.length}字');
      print('[DEBUG] 小说章节数: ${novel.chapters.length}');

      // 确保Hive盒子已打开
      if (!Hive.isBoxOpen(_novelsBoxName)) {
        print('[DEBUG] Hive盒子未打开，尝试重新打开');
        _novelsBox = await Hive.openBox(_novelsBoxName);
      }

      // 保存小说
      await _novelsBox.put(novelKey, novel);

      // 验证保存是否成功
      final savedNovel = _novelsBox.get(novelKey);
      if (savedNovel == null) {
        throw Exception('保存后无法验证小说数据');
      }

      print('[DEBUG] 保存小说成功: ${novel.title}');
    } catch (e, stackTrace) {
      print('[ERROR] 保存到Hive失败: $e');
      print('[ERROR] 错误堆栈: $stackTrace');
      rethrow;
    }
  }

  void _loadGeneratedChapters() {
    try {
      final savedChapters = _chaptersBox.get('chapters');
      if (savedChapters != null) {
        if (savedChapters is List) {
          _generatedChapters.value = savedChapters
              .map((chapterData) => chapterData is Chapter
                  ? chapterData
                  : Chapter.fromJson(Map<String, dynamic>.from(chapterData)))
              .toList();
        }
      }
    } catch (e) {
      print('加载章节失败: $e');
      _generatedChapters.clear();
    }
  }

  Future<void> saveChapter(String novelTitle, Chapter chapter) async {
    try {
      // 查找现有小说
      var novel = novels.firstWhere(
        (n) => n.title == novelTitle,
        orElse: () => Novel(
          title: novelTitle,
          genre: selectedGenres.join(','),
          outline: '',
          content: '',
          chapters: [],
          createdAt: DateTime.now(),
        ),
      );

      // 更新或添加章节
      var chapterIndex =
          novel.chapters.indexWhere((c) => c.number == chapter.number);
      if (chapterIndex != -1) {
        novel.chapters[chapterIndex] = chapter;
      } else {
        novel.chapters.add(chapter);
        // 按章节号排序
        novel.chapters.sort((a, b) => a.number.compareTo(b.number));
      }

      // 更新小说内容
      novel.content = novel.chapters.map((c) => c.content).join('\n\n');

      // 更新或添加小说到列表
      var novelIndex = novels.indexWhere((n) => n.title == novelTitle);
      if (novelIndex != -1) {
        novels[novelIndex] = novel;
      } else {
        novels.add(novel);
      }

      // 保存到本地存储
      await _saveToHive(novel);

      // 通知UI更新
      update();
    } catch (e) {
      print('保存章节失败: $e');
      rethrow;
    }
  }

  // 添加缺少的saveNovel方法
  Future<void> saveNovel(Novel novel) async {
    try {
      print('[DEBUG] 开始保存小说到内存列表和Hive...');
      print('[DEBUG] 小说标题: ${novel.title}');
      print('[DEBUG] 小说内容长度: ${novel.content.length}字');
      print('[DEBUG] 当前小说列表数量: ${novels.length}');

      // 检查小说是否已存在（通过ID而不是标题）
      var novelIndex = novels.indexWhere((n) => n.id == novel.id);
      if (novelIndex != -1) {
        print('[DEBUG] 更新现有小说，索引: $novelIndex');
        // 更新现有小说
        novels[novelIndex] = novel;
      } else {
        print('[DEBUG] 添加新小说');
        // 添加新小说
        novels.add(novel);
      }

      print('[DEBUG] 小说列表更新完成，当前数量: ${novels.length}');

      // 保存到本地存储
      print('[DEBUG] 开始保存到Hive...');
      await _saveToHive(novel);
      print('[DEBUG] Hive保存完成');

      // 通知UI更新
      print('[DEBUG] 通知UI更新...');
      update();

      print('[DEBUG] 小说保存完成: ${novel.title}');
    } catch (e, stackTrace) {
      print('[ERROR] 保存小说失败: $e');
      print('[ERROR] 错误堆栈: $stackTrace');
      rethrow;
    }
  }

  /// 更新小说
  Future<void> updateNovel(Novel novel) async {
    try {
      // 检查小说是否已存在
      var novelIndex = novels.indexWhere((n) => n.title == novel.title);
      if (novelIndex != -1) {
        // 更新现有小说
        novels[novelIndex] = novel;

        // 保存到本地存储
        await _saveToHive(novel);

        // 通知UI更新
        update();

        print('小说已更新: ${novel.title}');
      } else {
        throw Exception('找不到要更新的小说: ${novel.title}');
      }
    } catch (e) {
      print('更新小说失败: $e');
      rethrow;
    }
  }

  /// 获取当前小说
  Novel? getCurrentNovel() {
    // 如果当前有正在编辑的小说，返回该小说
    if (title.value.isNotEmpty) {
      final novelIndex = novels.indexWhere((n) => n.title == title.value);
      if (novelIndex != -1) {
        return novels[novelIndex];
      }
    }

    // 如果没有正在编辑的小说，返回null
    return null;
  }

  void updateTitle(String value) => title.value = value;
  void updateBackground(String value) => background.value = value;
  void updateOtherRequirements(String value) => otherRequirements.value = value;
  void updateStyle(String value) => style.value = value;
  void updateTargetReader(String value) => targetReader.value = value;

  // 短篇小说相关方法
  void setNovelType(NovelType type) {
    novelType.value = type;

    // 如果选择短篇小说，显示提示信息
    if (type == NovelType.shortNovel) {
      Get.snackbar(
        '短篇小说模式',
        '已启用三步式生成：世界观构建 → 详细大纲 → 分段生成',
        backgroundColor: Colors.green,
        colorText: Colors.white,
        duration: const Duration(seconds: 2),
      );
    }
  }

  void setShortNovelWordCount(ShortNovelWordCount wordCount) {
    shortNovelWordCount.value = wordCount;
  }

  void updateShortNovelWordCount(int count) {
    // 找到最接近的字数选项
    ShortNovelWordCount? closest;
    int minDiff = double.maxFinite.toInt();

    for (final option in ShortNovelWordCount.values) {
      final diff = (option.count - count).abs();
      if (diff < minDiff) {
        minDiff = diff;
        closest = option;
      }
    }

    if (closest != null) {
      shortNovelWordCount.value = closest;
    }
  }

  void updateTotalChapters(int value) {
    if (value > 0) {
      // 如果用户输入的值超过1000，给出提示但仍然允许设置
      if (value > 1000) {
        Get.snackbar(
          '提示',
          '章节数量较多，生成时间可能会较长，建议不要超过1000章',
          duration: const Duration(seconds: 5),
        );
      }
      _totalChapters.value = value;
    } else {
      Get.snackbar('错误', '章节数量必须大于0');
      _totalChapters.value = 1; // 设置为最小值
    }
  }

  void toggleGenre(String genre) {
    if (selectedGenres.contains(genre)) {
      selectedGenres.remove(genre);
    } else if (selectedGenres.length < 5) {
      selectedGenres.add(genre);
    }
  }

  void clearCache() async {
    print('清除所有 LangChain 相关缓存...');
    // _cacheService.clearAllCache(); // 注释掉或保留，取决于这个缓存的作用

    try {
      // 创建一个临时的 NovelMemory 实例并调用 clear
      // 如果有当前会话ID，只清除该会话的数据
      if (_currentSessionId.value.isNotEmpty) {
        await NovelMemory(
                novelTitle: 'temporary_for_clear',
                sessionId: _currentSessionId.value)
            .clear();
        Get.snackbar('缓存已清除', '已清除当前会话的 LangChain 数据。');
      } else {
        // 没有当前会话ID，清除所有数据
        await NovelMemory(novelTitle: 'temporary_for_clear').clear();
        Get.snackbar('缓存已清除', '已清除所有生成的 LangChain 数据。');
      }
    } catch (e) {
      print('清除 LangChain 缓存失败: $e');
      Get.snackbar('错误', '清除缓存失败: $e');
    }

    // 清除会话ID映射
    _novelSessionIds.clear();
    _currentSessionId.value = '';

    // 清除生成进度 (如果需要)
    // _novelGenerator.clearGenerationProgress();
  }

  void _updateRealtimeOutput(String text) {
    if (text.isEmpty) return;

    // 添加日志，帮助调试
    print('更新实时输出: ${text.length} 字符');

    // 确保在主线程更新UI
    Get.engine.addPostFrameCallback((_) {
      realtimeOutput.value += text;
      if (realtimeOutput.value.length > 10000) {
        realtimeOutput.value = realtimeOutput.value.substring(
          realtimeOutput.value.length - 10000,
        );
      }
    });
  }

  void _clearRealtimeOutput() {
    print('清除实时输出');
    realtimeOutput.value = '';
  }

  // 添加新的角色选择相关方法
  void toggleCharacterType(CharacterType type) {
    if (selectedCharacterTypes.contains(type)) {
      selectedCharacterTypes.remove(type);
      // 移除该类型下已选择的角色卡片
      selectedCharacterCards.remove(type.id);
    } else {
      selectedCharacterTypes.add(type);
      // 不需要初始化，因为现在是单个CharacterCard
    }
    // 强制触发响应式更新
    selectedCharacterCards.refresh();
  }

  void addCharacterCard(String typeId, CharacterCard card) {
    if (selectedCharacterCards[typeId] == null) {
      selectedCharacterCards[typeId] = <CharacterCard>[];
    }
    if (!selectedCharacterCards[typeId]!.any((c) => c.id == card.id)) {
      selectedCharacterCards[typeId]!.add(card);
      selectedCharacterCards.refresh();
    }
  }

  void removeCharacterCard(String typeId, CharacterCard card) {
    selectedCharacterCards[typeId]?.removeWhere((c) => c.id == card.id);
    if (selectedCharacterCards[typeId]?.isEmpty == true) {
      selectedCharacterCards.remove(typeId);
    }
    selectedCharacterCards.refresh();
  }

  void removeAllCharacterCardsForType(String typeId) {
    selectedCharacterCards.remove(typeId);
    selectedCharacterCards.refresh();
  }

  // 兼容旧的单选方法
  void setCharacterCard(String typeId, CharacterCard card) {
    selectedCharacterCards[typeId] = [card];
    selectedCharacterCards.refresh();
  }

  // 获取指定类型的第一个角色卡片（兼容旧代码）
  CharacterCard? getFirstCharacterCard(String typeId) {
    final cards = selectedCharacterCards[typeId];
    return cards?.isNotEmpty == true ? cards!.first : null;
  }

  // 获取指定类型的角色卡片
  CharacterCard? getCharacterCard(String typeId) {
    return getFirstCharacterCard(typeId);
  }

  // 获取所有角色卡片
  List<CharacterCard> getCharacterCards(String typeId) {
    return selectedCharacterCards[typeId] ?? [];
  }

  // 转换为兼容旧API的格式
  Map<String, CharacterCard> getCompatibleCharacterCards() {
    final result = <String, CharacterCard>{};
    selectedCharacterCards.forEach((typeId, cards) {
      if (cards.isNotEmpty) {
        result[typeId] = cards.first;
      }
    });
    return result;
  }

  // 获取角色设定字符串
  String getCharacterSettings() {
    final buffer = StringBuffer();

    for (final type in selectedCharacterTypes) {
      final cards = selectedCharacterCards[type.id];
      if (cards != null && cards.isNotEmpty) {
        buffer.writeln('${type.name}设定：');
        for (final card in cards) {
          buffer.writeln('姓名：${card.name}');
          if (card.gender?.isNotEmpty == true) {
            buffer.writeln('性别：${card.gender}');
          }
          if (card.age?.isNotEmpty == true) {
            buffer.writeln('年龄：${card.age}');
          }
          if (card.personalityTraits?.isNotEmpty == true) {
            buffer.writeln('性格：${card.personalityTraits}');
          }
          if (card.background?.isNotEmpty == true) {
            buffer.writeln('背景：${card.background}');
          }
          buffer.writeln();
        }
      }
    }

    return buffer.toString();
  }

  /// 导入大纲并转换为JSON格式
  Future<bool> importOutline(String outlineText,
      {bool isDetailedOutline = false}) async {
    if (title.value.isEmpty) {
      Get.snackbar('提示', '请先输入小说标题');
      return false;
    }

    try {
      // 使用增强版大纲导入服务
      final enhancedImportService = Get.find<EnhancedOutlineImportService>();

      // 清除当前大纲
      currentOutline.value = null;
      rawGeneratedOutlineText.value = '';

      // 导入大纲
      final outlineJson = await enhancedImportService.importOutline(
        outlineText: outlineText,
        novelTitle: title.value,
        isDetailedOutline: isDetailedOutline,
        onProgress: (status) {
          // 更新状态
        },
        onChapterProcessed: (processed, total) {
          // 更新进度
        },
      );

      if (outlineJson == null) {
        Get.snackbar('导入失败', '无法解析大纲内容');
        return false;
      }

      // 解析大纲JSON
      final List<ChapterOutline> chapterOutlines = [];

      if (outlineJson.containsKey('chapters') &&
          outlineJson['chapters'] is List) {
        final chapters = outlineJson['chapters'] as List;

        for (final chapter in chapters) {
          if (chapter is Map<String, dynamic>) {
            final chapterNumber = chapter['chapterNumber'] as int? ?? 0;
            final chapterTitle = chapter['chapterTitle'] as String? ?? '无标题';
            final summary = chapter['summary'] as String? ?? '无摘要';

            chapterOutlines.add(ChapterOutline(
              chapterNumber: chapterNumber,
              chapterTitle: chapterTitle,
              contentOutline: summary,
            ));
          }
        }
      }

      // 更新大纲
      currentOutline.value = NovelOutline(
        novelTitle: title.value,
        chapters: chapterOutlines,
      );

      // 生成可读的大纲文本
      final buffer = StringBuffer();
      buffer.writeln('小说标题：${title.value}');
      for (final chOutline in chapterOutlines) {
        buffer.writeln(
            '\n第${chOutline.chapterNumber}章：${chOutline.chapterTitle}');
        buffer.writeln(chOutline.contentOutline);
      }
      rawGeneratedOutlineText.value = buffer.toString();

      // 更新状态
      isUsingOutline.value = true;
      _totalChapters.value = chapterOutlines.length;

      // 重要：将大纲JSON保存到NovelMemory中，以便生成细纲时使用
      try {
        // 获取或创建会话ID
        final sessionId = _getOrCreateSessionId(title.value);
        _currentSessionId.value = sessionId;

        // 将大纲JSON转换为字符串
        final jsonEncoder = JsonEncoder.withIndent('  ');
        final outlineJsonString = jsonEncoder.convert(outlineJson);

        // 保存到NovelMemory - 使用标准化的小说标题
        final normalizedTitle = title.value.trim();

        // 1. 使用会话ID保存一份
        final novelMemoryWithSession =
            NovelMemory(novelTitle: normalizedTitle, sessionId: sessionId);
        await novelMemoryWithSession.saveOutline(outlineJsonString);

        // 2. 不使用会话ID再保存一份，确保在任何情况下都能找到
        final novelMemoryNoSession = NovelMemory(novelTitle: normalizedTitle);
        await novelMemoryNoSession.saveOutline(outlineJsonString);

        // 3. 打印调试信息
        print('大纲已保存到NovelMemory，标题: "$normalizedTitle", 会话ID: $sessionId');
        print('大纲JSON长度: ${outlineJsonString.length}');

        // 将细纲保存为第0章
        await _saveOutlineAsChapterZero();
      } catch (e) {
        // 即使保存失败也不影响导入成功
        print('保存大纲到NovelMemory失败: $e');
      }

      return true;
    } catch (e) {
      Get.snackbar('导入失败', '导入大纲时发生错误: $e');
      return false;
    }
  }

  // 生成或获取会话ID
  String _getOrCreateSessionId(String novelTitle) {
    // 如果已有会话ID，直接返回
    if (_novelSessionIds.containsKey(novelTitle)) {
      final sessionId = _novelSessionIds[novelTitle]!;
      print('使用现有会话ID: $sessionId (小说: $novelTitle)');
      _currentSessionId.value = sessionId;
      return sessionId;
    }

    // 创建新的会话ID
    final sessionId =
        'session_${DateTime.now().millisecondsSinceEpoch}_${novelTitle.hashCode}';
    _novelSessionIds[novelTitle] = sessionId;
    _currentSessionId.value = sessionId;
    print('创建新会话ID: $sessionId (小说: $novelTitle)');
    return sessionId;
  }

  // 增加generateNovel方法用于生成小说
  Future<void> generateNovel({
    bool continueGeneration = false,
  }) async {
    if (isGenerating.value) {
      Get.snackbar('提示', '正在生成中，请稍候...');
      return;
    }

    // 获取或创建会话ID
    final sessionId = _getOrCreateSessionId(title.value);

    if (continueGeneration) {
      // 续写功能：检查并继续生成未完成的章节
      await checkAndContinueGeneration();
      return;
    } else {
      // 根据小说类型选择不同的生成逻辑
      if (novelType.value == NovelType.shortNovel) {
        print('开始生成短篇小说...');
        await generateShortNovel();
      } else {
        // 默认情况下，生成长篇小说
        print('开始生成长篇小说...');

        // 先生成大纲
        if (currentOutline.value == null ||
            currentOutline.value!.chapters.isEmpty) {
          print('开始生成大纲...');
          await generateOutlineWrapper();

          // 等待用户确认大纲
          Get.snackbar('提示', '大纲已生成，请确认后点击"生成细纲"按钮。');
        } else {
          // 如果已有大纲，则开始生成章节
          await startGeneration();
        }
      }
    }
  }

  void addChapter(Chapter chapter) {
    _generatedChapters.add(chapter);
    _sortChapters();
    saveChapter(title.value, chapter);
  }

  void deleteChapter(int chapterNumber) {
    _generatedChapters
        .removeWhere((chapter) => chapter.number == chapterNumber);
    if (novels.isNotEmpty) {
      var novel = novels.firstWhere(
        (n) => n.title == title.value,
        orElse: () => Novel(
          title: title.value,
          genre: selectedGenres.join(','),
          outline: '',
          content: '',
          chapters: [],
          createdAt: DateTime.now(),
        ),
      );

      novel.chapters.removeWhere((chapter) => chapter.number == chapterNumber);
      _saveToHive(novel);
    }
  }

  void clearAllChapters() {
    _generatedChapters.clear();
    if (novels.isNotEmpty) {
      var novel = novels.firstWhere(
        (n) => n.title == title.value,
        orElse: () => Novel(
          title: title.value,
          genre: selectedGenres.join(','),
          outline: '',
          content: '',
          chapters: [],
          createdAt: DateTime.now(),
        ),
      );

      novel.chapters.clear();
      novel.content = '';
      _saveToHive(novel);
    }
  }

  void updateChapter(Chapter chapter) {
    final index =
        _generatedChapters.indexWhere((c) => c.number == chapter.number);
    if (index != -1) {
      _generatedChapters[index] = chapter;
      saveChapter(title.value, chapter);
    }
  }

  Chapter? getChapter(int chapterNumber) {
    return _generatedChapters
        .firstWhereOrNull((chapter) => chapter.number == chapterNumber);
  }

  void _sortChapters() {
    _generatedChapters.sort((a, b) => a.number.compareTo(b.number));
  }

  Future<String> exportChapters(
      String selectedFormat, List<Chapter> selectedChapters) async {
    if (title.isEmpty) {
      return '请先生成小说';
    }

    try {
      final novel = novels.firstWhere((n) => n.title == title.value);
      final result = await _exportService.exportNovel(
        novel,
        selectedFormat,
        selectedChapters: selectedChapters,
      );
      return result;
    } catch (e) {
      return '导出失败：$e';
    }
  }

  // 清除大纲
  void clearOutline() {
    currentOutline.value = null;
    isUsingOutline.value = false;
  }

  // 开始生成小说 (恢复功能)
  Future<void> startGeneration() async {
    if (isGenerating.value) {
      Get.snackbar('提示', '正在生成中，请稍候...');
      return;
    }

    // 检查是否有大纲
    if (currentOutline.value == null ||
        currentOutline.value!.chapters.isEmpty) {
      Get.snackbar('错误', '没有可用的大纲来生成章节。');
      return;
    }

    isGenerating.value = true;
    isPaused.value = false;
    _shouldStop.value = false;
    _clearRealtimeOutput();
    _updateRealtimeOutput('开始生成小说《${title.value}》...\n');
    generationStatus.value = '准备生成...';
    generationProgress.value = 0.0;

    // 初始化生成状态跟踪
    _generationStartTime.value = DateTime.now();
    _targetTotalChapters.value = currentOutline.value?.chapters.length ?? 0;
    _completedChapters.value = 0;
    isGenerationComplete.value = false;

    // 保存初始状态
    await _saveGenerationState();

    // 检查是否是续写模式 - 如果已有章节，说明是续写模式
    bool isContinuation = _generatedChapters.isNotEmpty;
    int existingChaptersCount = isContinuation ? _generatedChapters.length : 0;

    if (isContinuation) {
      _updateRealtimeOutput("检测到续写模式，已有 $existingChaptersCount 章内容\n");
    }

    final totalChaptersToGenerate = currentOutline.value!.chapters.length;

    try {
      // 计算需要生成的章节数量（排除已有章节）
      int chaptersToGenerate = totalChaptersToGenerate;
      if (isContinuation) {
        chaptersToGenerate = totalChaptersToGenerate - existingChaptersCount;
        _updateRealtimeOutput("需要生成 $chaptersToGenerate 个新章节\n");
      }

      int generatedCount = 0;

      for (int i = 0; i < totalChaptersToGenerate; i++) {
        // 检查是否需要停止或暂停
        if (_shouldStop.value) {
          generationStatus.value = '已停止';
          isGenerating.value = false;
          print('生成已停止');
          return;
        }

        // 检查暂停状态并等待
        await _checkPauseState();

        final chapterOutline = currentOutline.value!.chapters[i];
        final chapterNumber = chapterOutline.chapterNumber;

        // 如果是续写模式，跳过已有章节
        if (isContinuation) {
          // 检查章节是否已存在
          bool chapterExists =
              _generatedChapters.any((ch) => ch.number == chapterNumber);
          if (chapterExists) {
            _updateRealtimeOutput(
                "\n跳过第 $chapterNumber 章《${chapterOutline.chapterTitle}》，已有内容\n");
            continue;
          }
        }

        _currentChapter.value = chapterNumber;
        final status =
            '正在生成第 ${_currentChapter.value} 章: ${chapterOutline.chapterTitle}...';
        generationStatus.value = status;
        _updateRealtimeOutput('\n-- $status --\n');

        // 调用 langChain 服务生成章节
        await generateChapterFromOutline(
          chapterNumber: _currentChapter.value,
          // outlineString: null, // 使用 currentOutline.value
          onStatus: (s) => generationStatus.value = s,
          novelTitle: title.value, // 传递当前小说标题
          sessionId: currentSessionId, // 传递当前会话ID
        );

        generatedCount++;
        _completedChapters.value++;

        // 更新进度 - 基于实际需要生成的章节数量
        if (isContinuation) {
          generationProgress.value = generatedCount / chaptersToGenerate;
        } else {
          generationProgress.value = (i + 1) / totalChaptersToGenerate;
        }

        // 定期保存状态
        if (_completedChapters.value % 3 == 0 ||
            _completedChapters.value == _targetTotalChapters.value) {
          await _saveGenerationState();
        }

        // 短暂延时，避免UI卡顿和API调用过于频繁
        await Future.delayed(const Duration(milliseconds: 500));
      }

      generationStatus.value = '小说生成完成！';
      generationProgress.value = 1.0;
      isGenerationComplete.value = true;
      _updateRealtimeOutput('\n\n小说生成完成！');

      // 设置生成完成状态
      generationStage.value = GenerationStage.generationComplete;

      // 清理生成状态（完成后不需要保存状态）
      await _clearGenerationState();

      // 所有章节已在生成过程中实时保存到书库中
      Get.snackbar('生成成功', '小说《${title.value}》已生成完成并保存到书库中');
    } catch (e) {
      generationStatus.value = '生成失败: $e';
      _updateRealtimeOutput('\n\n生成失败: $e');
      print('生成小说时出错: $e');
      // 可以选择显示 Snackbar 提示用户
      Get.snackbar('生成错误', '生成过程中发生错误: $e');
    } finally {
      isGenerating.value = false;
      isPaused.value = false; // 确保结束时重置暂停状态
      _currentChapter.value = 0; // 重置当前章节
    }
  }

  // 检查并继续生成的方法 - 实现续写功能
  Future<void> checkAndContinueGeneration() async {
    if (!isPaused.value) return;

    try {
      // 重置暂停状态
      isPaused.value = false;

      // 如果有当前小说，继续生成
      if (currentOutline.value != null) {
        // 查找未生成的章节
        await continueNovelGeneration();
      } else {
        Get.snackbar('错误', '无法继续生成，未找到当前小说的大纲信息');
        _resetGenerationState();
      }
    } catch (e) {
      Get.snackbar('错误', '操作失败：$e');
      _resetGenerationState();
    }
  }

  /// 检查并继续生成小说 - 用于书库中的续写功能
  /// 返回 'completed' 表示所有章节已生成完毕，'generating' 表示正在生成章节
  Future<String> checkAndContinueNovelGeneration(Novel novel) async {
    try {
      // 设置当前小说信息
      title.value = novel.title;

      // 解析大纲信息
      if (novel.outline.isNotEmpty) {
        // 尝试从小说对象中恢复大纲
        await _recoverOutlineFromNovel(novel);
      }

      // 检查是否有大纲
      if (currentOutline.value == null ||
          currentOutline.value!.chapters.isEmpty) {
        Get.snackbar('错误', '无法续写，未找到小说大纲信息');
        return 'error';
      }

      // 获取大纲中的所有章节
      final outlineChapters = currentOutline.value!.chapters;

      // 获取已生成的章节
      final generatedChapterNumbers =
          novel.chapters.map((ch) => ch.number).toSet();

      // 找出未生成的章节
      final missingChapters = <int>[];
      for (final chapter in outlineChapters) {
        if (!generatedChapterNumbers.contains(chapter.chapterNumber)) {
          missingChapters.add(chapter.chapterNumber);
        }
      }

      // 按章节号排序
      missingChapters.sort();

      if (missingChapters.isEmpty) {
        // 所有章节已生成完毕
        return 'completed';
      }

      // 提示用户有未生成的章节
      final result = await Get.dialog<bool>(
        AlertDialog(
          title: const Text('发现未生成章节'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text('检测到以下章节尚未生成：'),
              const SizedBox(height: 8),
              ...missingChapters.map((chapterNumber) {
                final chapterOutline = outlineChapters.firstWhere(
                  (ch) => ch.chapterNumber == chapterNumber,
                );
                return Text(
                    '- 第$chapterNumber章：${chapterOutline.chapterTitle}');
              }),
              const SizedBox(height: 16),
              const Text('需要先补写这些章节才能继续。是否立即开始补写？'),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Get.back(result: false),
              child: const Text('取消'),
            ),
            ElevatedButton(
              onPressed: () => Get.back(result: true),
              child: const Text('开始补写'),
            ),
          ],
        ),
      );

      if (result == true) {
        // 用户选择开始补写
        // 保存当前小说到控制器
        _generatedChapters.clear();
        for (final chapter in novel.chapters) {
          _generatedChapters.add(chapter);
        }

        print('[续写调试] 初始化_generatedChapters，共${_generatedChapters.length}章');
        print(
            '[续写调试] 现有章节号: ${_generatedChapters.map((ch) => ch.number).toList()}');

        // 开始生成未生成的章节
        await continueNovelGeneration();
        return 'generating';
      } else {
        // 用户取消补写
        return 'cancelled';
      }
    } catch (e) {
      Get.snackbar('错误', '检查未生成章节失败: $e');
      return 'error';
    }
  }

  /// 从小说对象中恢复大纲 (内部方法)
  Future<void> _recoverOutlineFromNovel(Novel novel) async {
    try {
      // 如果大纲为空，无法恢复
      if (novel.outline.isEmpty) return;

      // 尝试从第0章获取大纲
      final outlineChapter =
          novel.chapters.firstWhereOrNull((ch) => ch.number == 0);
      if (outlineChapter != null) {
        // 从第0章解析大纲
        await _parseOutlineFromChapterZero(outlineChapter.content);
        return;
      }

      // 如果没有第0章，尝试从小说大纲字段解析
      await _parseOutlineFromString(novel.outline);
    } catch (e) {
      print('从小说恢复大纲失败: $e');
    }
  }

  /// 从小说对象恢复大纲 (公开方法，用于续写功能)
  Future<void> restoreOutlineFromNovel(Novel novel) async {
    try {
      // 设置生成阶段为idle，以便后续可以正确处理
      generationStage.value = GenerationStage.idle;

      // 尝试从小说的outline字段恢复大纲
      if (novel.outline.isNotEmpty) {
        // 尝试解析JSON格式的大纲
        try {
          final outlineJson = jsonDecode(novel.outline);
          if (outlineJson is Map<String, dynamic> &&
              outlineJson.containsKey('novelTitle') &&
              outlineJson.containsKey('chapters')) {
            final chapters = <ChapterOutline>[];
            for (final chapterJson in outlineJson['chapters']) {
              chapters.add(ChapterOutline.fromJson(chapterJson));
            }
            currentOutline.value = NovelOutline(
              novelTitle: outlineJson['novelTitle'],
              chapters: chapters,
            );
            print('从小说JSON恢复大纲成功');

            // 设置生成阶段为detailedOutlineReady，表示已有细纲
            generationStage.value = GenerationStage.detailedOutlineReady;
            return;
          }
        } catch (e) {
          print('解析大纲JSON失败，尝试其他方式: $e');
        }

        // 如果JSON解析失败，尝试从文本格式解析
        await _parseOutlineFromString(novel.outline);
        if (currentOutline.value != null) {
          print('从小说文本恢复大纲成功');

          // 设置生成阶段为outlineReady，表示已有大纲
          generationStage.value = GenerationStage.outlineReady;
          return;
        }
      }

      // 如果outline字段为空或解析失败，尝试从第0章内容解析
      if (novel.chapters.isNotEmpty) {
        final chapterZero =
            novel.chapters.firstWhereOrNull((ch) => ch.number == 0);
        if (chapterZero != null && chapterZero.content.isNotEmpty) {
          await _parseOutlineFromChapterZero(chapterZero.content);
          if (currentOutline.value != null) {
            print('从第0章恢复大纲成功');

            // 设置生成阶段为outlineReady，表示已有大纲
            generationStage.value = GenerationStage.outlineReady;
            return;
          }
        }
      }

      // 如果所有尝试都失败，创建一个基本的大纲
      if (currentOutline.value == null && novel.chapters.isNotEmpty) {
        final chapters = <ChapterOutline>[];
        for (final chapter in novel.chapters.where((ch) => ch.number > 0)) {
          chapters.add(ChapterOutline(
            chapterNumber: chapter.number,
            chapterTitle: chapter.title,
            contentOutline: '章节内容概要（自动生成）',
          ));
        }
        if (chapters.isNotEmpty) {
          currentOutline.value = NovelOutline(
            novelTitle: novel.title,
            chapters: chapters,
          );
          print('创建基本大纲成功');

          // 设置生成阶段为detailedOutlineReady，表示已有细纲
          generationStage.value = GenerationStage.detailedOutlineReady;
          return;
        }
      }

      // 如果仍然无法恢复大纲，显示错误
      if (currentOutline.value == null) {
        Get.snackbar('警告', '无法恢复小说大纲，可能需要重新生成');
      } else {
        // 如果成功恢复了大纲，设置生成阶段为detailedOutlineReady
        generationStage.value = GenerationStage.detailedOutlineReady;
      }
    } catch (e) {
      print('从小说恢复大纲失败: $e');
      Get.snackbar('错误', '恢复小说大纲失败: $e');
    }
  }

  /// 准备小说续写 - 恢复大纲并准备扩展
  Future<void> prepareNovelForContinuation(
      Novel novel, int targetTotalChapters) async {
    try {
      // 首先恢复现有大纲
      await restoreOutlineFromNovel(novel);

      // 获取当前大纲中的章节数
      final currentChaptersCount = currentOutline.value?.chapters.length ?? 0;

      // 如果目标章节数小于等于当前章节数，无需扩展大纲
      if (targetTotalChapters <= currentChaptersCount) {
        Get.snackbar(
          '提示',
          '目标章节数 $targetTotalChapters 小于或等于当前大纲章节数 $currentChaptersCount，无需扩展大纲',
          duration: const Duration(seconds: 3),
        );
        return;
      }

      // 保存当前大纲，以便后续扩展
      rawGeneratedOutlineText.value = jsonEncode(currentOutline.value);

      // 设置生成阶段为idle，准备生成新的大纲
      generationStage.value = GenerationStage.idle;

      // 将已有章节加载到生成章节列表中
      _generatedChapters.clear();
      for (final chapter in novel.chapters.where((ch) => ch.number > 0)) {
        _generatedChapters.add(chapter);
      }

      // 向量化已有章节内容，以便后续生成时使用
      try {
        final vectorizationService = Get.find<NovelVectorizationService>();
        if (!vectorizationService.isNovelVectorized(novel.title)) {
          Get.snackbar(
            '正在向量化小说内容',
            '这将帮助AI更好地理解已有内容，生成更连贯的后续章节',
            duration: const Duration(seconds: 3),
          );
          await vectorizationService.vectorizeNovel(novel.title);
        }
      } catch (e) {
        print('向量化小说内容失败: $e');
        // 即使向量化失败，也继续后续步骤
      }

      // 更新总章节数
      _totalChapters.value = targetTotalChapters;

      Get.snackbar(
        '准备完成',
        '已准备好扩展《${novel.title}》到 $targetTotalChapters 章，请点击"生成大纲"按钮开始扩展',
        duration: const Duration(seconds: 3),
      );
    } catch (e) {
      print('准备小说续写失败: $e');
      Get.snackbar('错误', '准备小说续写失败: $e');
    }
  }

  /// 从第0章内容解析大纲
  Future<void> _parseOutlineFromChapterZero(String content) async {
    try {
      // 简单解析大纲内容
      final lines = content.split('\n');
      String novelTitle = '';
      final chapters = <ChapterOutline>[];

      int currentChapterNumber = 0;
      String currentChapterTitle = '';
      StringBuffer currentContentOutline = StringBuffer();

      for (final line in lines) {
        if (line.startsWith('小说标题：')) {
          novelTitle = line.substring('小说标题：'.length).trim();
        } else if (line.contains('第') && line.contains('章：')) {
          // 如果已经有章节信息，保存上一章
          if (currentChapterNumber > 0 && currentContentOutline.isNotEmpty) {
            chapters.add(ChapterOutline(
              chapterNumber: currentChapterNumber,
              chapterTitle: currentChapterTitle,
              contentOutline: currentContentOutline.toString().trim(),
            ));
            currentContentOutline.clear();
          }

          // 解析新章节
          final match = RegExp(r'第(\d+)章：(.+)').firstMatch(line);
          if (match != null) {
            currentChapterNumber = int.parse(match.group(1)!);
            currentChapterTitle = match.group(2)!.trim();
          }
        } else if (currentChapterNumber > 0) {
          // 添加到当前章节大纲
          currentContentOutline.writeln(line);
        }
      }

      // 保存最后一章
      if (currentChapterNumber > 0 && currentContentOutline.isNotEmpty) {
        chapters.add(ChapterOutline(
          chapterNumber: currentChapterNumber,
          chapterTitle: currentChapterTitle,
          contentOutline: currentContentOutline.toString().trim(),
        ));
      }

      // 创建大纲对象
      if (novelTitle.isNotEmpty && chapters.isNotEmpty) {
        currentOutline.value = NovelOutline(
          novelTitle: novelTitle,
          chapters: chapters,
        );
      }
    } catch (e) {
      print('解析第0章大纲失败: $e');
    }
  }

  /// 从字符串解析大纲
  Future<void> _parseOutlineFromString(String outlineString) async {
    try {
      // 简单解析大纲字符串
      final lines = outlineString.split('\n');
      String novelTitle = '';
      final chapters = <ChapterOutline>[];

      int currentChapterNumber = 0;
      String currentChapterTitle = '';
      StringBuffer currentContentOutline = StringBuffer();

      for (final line in lines) {
        if (line.startsWith('小说标题：')) {
          novelTitle = line.substring('小说标题：'.length).trim();
        } else if (line.contains('第') && line.contains('章：')) {
          // 如果已经有章节信息，保存上一章
          if (currentChapterNumber > 0 && currentContentOutline.isNotEmpty) {
            chapters.add(ChapterOutline(
              chapterNumber: currentChapterNumber,
              chapterTitle: currentChapterTitle,
              contentOutline: currentContentOutline.toString().trim(),
            ));
            currentContentOutline.clear();
          }

          // 解析新章节
          final match = RegExp(r'第(\d+)章：(.+)').firstMatch(line);
          if (match != null) {
            currentChapterNumber = int.parse(match.group(1)!);
            currentChapterTitle = match.group(2)!.trim();
          }
        } else if (currentChapterNumber > 0) {
          // 添加到当前章节大纲
          currentContentOutline.writeln(line);
        }
      }

      // 保存最后一章
      if (currentChapterNumber > 0 && currentContentOutline.isNotEmpty) {
        chapters.add(ChapterOutline(
          chapterNumber: currentChapterNumber,
          chapterTitle: currentChapterTitle,
          contentOutline: currentContentOutline.toString().trim(),
        ));
      }

      // 创建大纲对象
      if (novelTitle.isNotEmpty && chapters.isNotEmpty) {
        currentOutline.value = NovelOutline(
          novelTitle: novelTitle,
          chapters: chapters,
        );
      }
    } catch (e) {
      print('解析大纲字符串失败: $e');
    }
  }

  // 增强的暂停生成方法
  void stopGeneration() {
    if (!isGenerating.value) return;

    isPaused.value = true;
    _updateRealtimeOutput('\n已暂停生成，当前进度：第${_currentChapter.value}章\n');

    // 保存暂停状态到本地存储
    _saveGenerationState();

    Get.snackbar(
      '已暂停',
      '生成已暂停，可以点击"继续生成"按钮恢复，或在书库中继续',
      duration: const Duration(seconds: 3),
    );
  }

  // 新增：检查暂停状态的方法
  Future<void> _checkPauseState() async {
    if (isPaused.value) {
      // 如果没有暂停完成器，创建一个
      if (_pauseCompleter.value == null || _pauseCompleter.value!.isCompleted) {
        _pauseCompleter.value = Completer<void>();
      }

      // 等待暂停完成器被完成（即继续生成）
      await _pauseCompleter.value!.future;
    }
  }

  // 新增：继续生成的方法
  void resumeGeneration() {
    if (!isPaused.value) return;

    isPaused.value = false;

    // 完成暂停等待
    if (_pauseCompleter.value != null && !_pauseCompleter.value!.isCompleted) {
      _pauseCompleter.value!.complete();
    }

    _updateRealtimeOutput('\n继续生成...\n');
    Get.snackbar('继续生成', '已恢复生成进程');
  }

  // 新增：保存生成状态到本地存储
  Future<void> _saveGenerationState() async {
    try {
      final box = await Hive.openBox('generation_state');
      final stateData = {
        'sessionId': currentSessionId,
        'title': title.value,
        'genres': selectedGenres,
        'background': background.value,
        'otherRequirements': otherRequirements.value,
        'targetReader': targetReader.value,
        'selectedWritingStyle': selectedWritingStyle.value,
        'totalChapters': totalChapters,
        'currentChapter': _currentChapter.value,
        'targetTotalChapters': _targetTotalChapters.value,
        'completedChapters': _completedChapters.value,
        'isPaused': isPaused.value,
        'isGenerating': isGenerating.value,
        'generationStartTime': _generationStartTime.value?.toIso8601String(),
        'lastSaveTime': DateTime.now().toIso8601String(),
        'novelType': novelType.value.name,
        'characterCards': selectedCharacterCards.map((key, value) =>
            MapEntry(key, value.map((card) => card.toJson()).toList())),
      };

      await box.put(currentSessionId, stateData);
      _lastSaveTime.value = DateTime.now();
      print('生成状态已保存: ${currentSessionId}');
    } catch (e) {
      print('保存生成状态失败: $e');
    }
  }

  // 新增：检查小说生成完成状态
  bool isNovelGenerationComplete(Novel novel) {
    try {
      // 检查是否有大纲
      if (currentOutline.value == null) {
        // 如果没有大纲，检查小说是否有章节
        return novel.chapters.isNotEmpty;
      }

      // 有大纲的情况下，比较实际章节数和大纲章节数
      final outlineChapterCount = currentOutline.value!.chapters.length;
      final actualChapterCount = novel.chapters.length;

      // 检查是否所有大纲章节都有对应的实际章节
      final outlineChapterNumbers =
          currentOutline.value!.chapters.map((ch) => ch.chapterNumber).toSet();
      final actualChapterNumbers =
          novel.chapters.map((ch) => ch.number).toSet();

      // 如果所有大纲章节都有对应的实际章节，则认为生成完成
      final isComplete = outlineChapterNumbers
          .every((num) => actualChapterNumbers.contains(num));

      print('[生成状态检查] 大纲章节数: $outlineChapterCount, 实际章节数: $actualChapterCount');
      print('[生成状态检查] 大纲章节号: $outlineChapterNumbers');
      print('[生成状态检查] 实际章节号: $actualChapterNumbers');
      print('[生成状态检查] 是否完成: $isComplete');

      return isComplete;
    } catch (e) {
      print('[生成状态检查] 检查失败: $e');
      return false;
    }
  }

  // 新增：获取小说生成进度
  double getNovelGenerationProgress(Novel novel) {
    try {
      if (currentOutline.value == null) {
        return novel.chapters.isNotEmpty ? 1.0 : 0.0;
      }

      final outlineChapterCount = currentOutline.value!.chapters.length;
      if (outlineChapterCount == 0) return 0.0;

      final outlineChapterNumbers =
          currentOutline.value!.chapters.map((ch) => ch.chapterNumber).toSet();
      final actualChapterNumbers =
          novel.chapters.map((ch) => ch.number).toSet();

      final completedCount = outlineChapterNumbers
          .where((num) => actualChapterNumbers.contains(num))
          .length;

      return completedCount / outlineChapterCount;
    } catch (e) {
      print('[进度计算] 计算失败: $e');
      return 0.0;
    }
  }

  // 新增：获取小说生成状态枚举
  NovelGenerationStatus getNovelGenerationStatus(Novel novel) {
    try {
      // 检查是否有保存的生成状态
      final sessionId = novel.sessionId;
      if (sessionId != null && sessionId.isNotEmpty) {
        // 检查是否有暂停状态
        if (isPaused.value && currentSessionId == sessionId) {
          return NovelGenerationStatus.paused;
        }

        // 检查是否正在生成
        if (isGenerating.value && currentSessionId == sessionId) {
          return NovelGenerationStatus.generating;
        }
      }

      // 检查是否生成完成
      if (isNovelGenerationComplete(novel)) {
        return NovelGenerationStatus.completed;
      }

      // 检查是否有部分章节（中断状态）
      if (novel.chapters.isNotEmpty) {
        return NovelGenerationStatus.interrupted;
      }

      // 没有任何章节
      return NovelGenerationStatus.notStarted;
    } catch (e) {
      print('[状态获取] 获取失败: $e');
      return NovelGenerationStatus.notStarted;
    }
  }

  // 新增：获取所有未完成生成的小说
  List<Novel> getIncompleteNovels() {
    try {
      final incompleteNovels = <Novel>[];

      for (final novel in novels) {
        final status = getNovelGenerationStatus(novel);
        if (status == NovelGenerationStatus.paused ||
            status == NovelGenerationStatus.interrupted ||
            status == NovelGenerationStatus.generating) {
          incompleteNovels.add(novel);
        }
      }

      print('[未完成小说] 找到 ${incompleteNovels.length} 部未完成的小说');
      return incompleteNovels;
    } catch (e) {
      print('[未完成小说] 获取失败: $e');
      return [];
    }
  }

  // 新增：检查是否有保存的生成状态
  Future<bool> hasGenerationState(String sessionId) async {
    try {
      final box = await Hive.openBox('generation_state');
      return box.containsKey(sessionId);
    } catch (e) {
      print('[状态检查] 检查失败: $e');
      return false;
    }
  }

  // 新增：从书库恢复小说生成状态（公共方法）
  Future<bool> restoreNovelGenerationFromLibrary(Novel novel) async {
    try {
      final sessionId = novel.sessionId;
      if (sessionId == null || sessionId.isEmpty) {
        print('[恢复生成] 小说没有会话ID，无法恢复');
        return false;
      }

      // 检查是否有保存的生成状态
      if (!await hasGenerationState(sessionId)) {
        print('[恢复生成] 没有找到保存的生成状态: $sessionId');
        return false;
      }

      // 恢复生成状态
      final restored = await _restoreGenerationState(sessionId);
      if (!restored) {
        print('[恢复生成] 恢复生成状态失败');
        return false;
      }

      // 恢复小说的大纲信息
      // 注意：Novel模型中的outline是String类型，需要从其他地方恢复NovelOutline
      // 这里我们依赖于_restoreGenerationState中恢复的状态
      if (currentOutline.value != null) {
        isUsingOutline.value = true;
        _hasOutline.value = true;
      }

      // 设置当前小说信息
      title.value = novel.title;

      // 计算当前进度
      _completedChapters.value = novel.chapters.length;
      _targetTotalChapters.value =
          currentOutline.value?.chapters.length ?? novel.chapters.length;

      // 检查是否已经完成
      if (isNovelGenerationComplete(novel)) {
        isGenerationComplete.value = true;
        isPaused.value = false;
        print('[恢复生成] 小说已完成，无需继续生成');
        return true;
      }

      print('[恢复生成] 成功恢复小说生成状态: ${novel.title}');
      print(
          '[恢复生成] 当前进度: ${_completedChapters.value}/${_targetTotalChapters.value}');

      return true;
    } catch (e) {
      print('[恢复生成] 恢复失败: $e');
      return false;
    }
  }

  // 新增：继续生成指定小说（从书库调用）
  Future<void> continueNovelGenerationFromLibrary(Novel novel) async {
    try {
      // 首先恢复生成状态
      final restored = await restoreNovelGenerationFromLibrary(novel);
      if (!restored) {
        Get.snackbar('错误', '无法恢复小说生成状态');
        return;
      }

      // 如果已经完成，提示用户
      if (isGenerationComplete.value) {
        Get.snackbar('提示', '小说《${novel.title}》已生成完成');
        return;
      }

      // 如果正在生成，提示用户
      if (isGenerating.value) {
        Get.snackbar('提示', '小说正在生成中，请稍候...');
        return;
      }

      // 开始继续生成
      Get.snackbar('继续生成', '正在恢复小说《${novel.title}》的生成...');

      // 调用续写方法
      await continueNovelGeneration();
    } catch (e) {
      print('[继续生成] 失败: $e');
      Get.snackbar('错误', '继续生成失败: $e');
    }
  }

  // 新增：应用启动时检查并恢复生成状态
  Future<void> _checkAndRestoreGenerationStates() async {
    try {
      print('[状态恢复] 开始检查未完成的生成任务...');

      final box = await Hive.openBox('generation_state');
      final allKeys = box.keys.toList();

      if (allKeys.isEmpty) {
        print('[状态恢复] 没有找到保存的生成状态');
        return;
      }

      print('[状态恢复] 找到 ${allKeys.length} 个保存的生成状态');

      // 检查每个保存的状态
      for (final sessionId in allKeys) {
        try {
          final stateData = box.get(sessionId) as Map<String, dynamic>?;
          if (stateData == null) continue;

          final novelTitle = stateData['title'] as String?;
          final isPausedState = stateData['isPaused'] as bool? ?? false;
          final lastSaveTime = stateData['lastSaveTime'] as String?;

          if (novelTitle == null || novelTitle.isEmpty) continue;

          // 查找对应的小说
          final novel = novels.firstWhereOrNull((n) => n.title == novelTitle);
          if (novel == null) {
            print('[状态恢复] 未找到对应小说: $novelTitle，清理状态');
            await box.delete(sessionId);
            continue;
          }

          // 检查是否是暂停状态
          if (isPausedState) {
            print('[状态恢复] 发现暂停的生成任务: $novelTitle');

            // 检查最后保存时间，如果超过24小时，询问用户是否继续
            if (lastSaveTime != null) {
              final saveTime = DateTime.parse(lastSaveTime);
              final hoursSinceLastSave =
                  DateTime.now().difference(saveTime).inHours;

              if (hoursSinceLastSave > 24) {
                print('[状态恢复] 生成任务已暂停超过24小时: $novelTitle');
                // 这里可以添加用户确认逻辑
              }
            }
          }
        } catch (e) {
          print('[状态恢复] 处理状态失败: $sessionId, $e');
        }
      }

      print('[状态恢复] 状态检查完成');
    } catch (e) {
      print('[状态恢复] 检查失败: $e');
    }
  }

  // 新增：清理当前生成状态
  Future<void> _clearGenerationState() async {
    try {
      final box = await Hive.openBox('generation_state');
      await box.delete(currentSessionId);
      isParametersRestored.value = false;
      print('[状态清理] 已清理生成状态: $currentSessionId');
    } catch (e) {
      print('[状态清理] 清理失败: $e');
    }
  }

  // 新增：清理过期的生成状态
  Future<void> cleanupExpiredGenerationStates() async {
    try {
      final box = await Hive.openBox('generation_state');
      final allKeys = box.keys.toList();
      final now = DateTime.now();
      int cleanedCount = 0;

      for (final sessionId in allKeys) {
        try {
          final stateData = box.get(sessionId) as Map<String, dynamic>?;
          if (stateData == null) continue;

          final lastSaveTime = stateData['lastSaveTime'] as String?;
          if (lastSaveTime == null) continue;

          final saveTime = DateTime.parse(lastSaveTime);
          final daysSinceLastSave = now.difference(saveTime).inDays;

          // 清理超过7天的状态
          if (daysSinceLastSave > 7) {
            await box.delete(sessionId);
            cleanedCount++;
            print('[状态清理] 清理过期状态: $sessionId');
          }
        } catch (e) {
          print('[状态清理] 处理失败: $sessionId, $e');
        }
      }

      if (cleanedCount > 0) {
        print('[状态清理] 清理了 $cleanedCount 个过期状态');
      }
    } catch (e) {
      print('[状态清理] 清理失败: $e');
    }
  }

  // 新增：从本地存储恢复生成状态
  Future<bool> _restoreGenerationState(String sessionId) async {
    try {
      final box = await Hive.openBox('generation_state');
      final stateData = box.get(sessionId) as Map<String, dynamic>?;

      if (stateData == null) return false;

      // 恢复基本参数
      title.value = stateData['title'] ?? '';
      selectedGenres.assignAll(List<String>.from(stateData['genres'] ?? []));
      background.value = stateData['background'] ?? '';
      otherRequirements.value = stateData['otherRequirements'] ?? '';
      targetReader.value = stateData['targetReader'] ?? '';
      selectedWritingStyle.value = stateData['selectedWritingStyle'] ?? '';
      _totalChapters.value = stateData['totalChapters'] ?? 10;
      _currentChapter.value = stateData['currentChapter'] ?? 0;
      _targetTotalChapters.value = stateData['targetTotalChapters'] ?? 0;
      _completedChapters.value = stateData['completedChapters'] ?? 0;

      // 恢复生成状态
      isPaused.value = stateData['isPaused'] ?? false;
      isGenerating.value = false; // 重启后不应该是生成中状态

      // 恢复时间信息
      if (stateData['generationStartTime'] != null) {
        _generationStartTime.value =
            DateTime.parse(stateData['generationStartTime']);
      }
      if (stateData['lastSaveTime'] != null) {
        _lastSaveTime.value = DateTime.parse(stateData['lastSaveTime']);
      }

      // 恢复小说类型
      if (stateData['novelType'] != null) {
        novelType.value = NovelType.values.firstWhere(
          (type) => type.name == stateData['novelType'],
          orElse: () => NovelType.longNovel,
        );
      }

      // 恢复角色卡片
      if (stateData['characterCards'] != null) {
        final cardsData =
            Map<String, dynamic>.from(stateData['characterCards']);
        selectedCharacterCards.clear();
        cardsData.forEach((key, value) {
          try {
            final cardsList = List<dynamic>.from(value);
            selectedCharacterCards[key] = cardsList
                .map((cardData) =>
                    CharacterCard.fromJson(Map<String, dynamic>.from(cardData)))
                .toList();
          } catch (e) {
            print('恢复角色卡片失败: $key, $e');
          }
        });
      }

      _currentSessionId.value = sessionId;
      isParametersRestored.value = true; // 标记参数已恢复
      print('生成状态已恢复: $sessionId');
      return true;
    } catch (e) {
      print('恢复生成状态失败: $e');
      return false;
    }
  }

  // 添加开始新小说的方法
  void startNewNovel() {
    // 清除当前小说的状态，但保留所有缓存数据
    print('开始新小说，清除当前状态，但保留缓存');

    // 保存当前会话ID，以便后续使用
    String currentSessionId = _currentSessionId.value;
    String currentTitle = title.value;

    // 清除输入状态
    title.value = '';
    background.value = '';
    otherRequirements.value = '';
    selectedGenres.clear();
    selectedCharacterTypes.clear();
    selectedCharacterCards.clear();

    // 清除生成状态
    _clearRealtimeOutput();
    generationStatus.value = '';
    generationProgress.value = 0.0;
    _currentChapter.value = 0;
    _hasOutline.value = false;

    // 清除大纲状态
    currentOutline.value = null;
    rawGeneratedOutlineText.value = '';
    isUsingOutline.value = false;

    // 清除生成的章节
    _generatedChapters.clear();

    // 清除短篇小说相关状态
    currentShortNovelOutline.value = null;
    shortNovelGeneratedContent.value = '';
    shortNovelCurrentPart.value = 0;

    // 重置生成阶段
    generationStage.value = GenerationStage.idle;

    // 重置当前会话ID，但不清除缓存
    _currentSessionId.value = '';

    // 通知用户
    Get.snackbar(
      '已重置',
      '当前状态已清除，可以开始创作新小说',
      duration: const Duration(seconds: 2),
    );
  }

  // 删除小说
  Future<void> deleteNovel(Novel novel) async {
    try {
      // 从列表中移除
      novels.remove(novel);

      // 从本地存储中删除
      final novelKey = 'novel_${novel.title}';
      await _novelsBox.delete(novelKey);

      // 如果是当前正在生成的小说，清除相关状态
      if (title.value == novel.title) {
        startNewNovel();
      }

      print('删除小说成功: ${novel.title}');
      Get.snackbar('成功', '已删除《${novel.title}》');
    } catch (e) {
      print('删除小说失败: $e');
      Get.snackbar('错误', '删除失败：$e');
    }
  }

  // 加载所有保存的小说
  Future<void> loadNovels() async {
    try {
      print('[DEBUG] 开始加载小说列表');

      // 确保Hive盒子已打开
      if (!Hive.isBoxOpen(_novelsBoxName)) {
        print('[DEBUG] Hive盒子未打开，尝试重新打开');
        _novelsBox = await Hive.openBox(_novelsBoxName);
      }

      // 获取所有以novel_开头的键
      final keys = _novelsBox.keys
          .where((key) => key.toString().startsWith('novel_'))
          .toList();
      print('[DEBUG] 找到 ${keys.length} 个小说键');

      final loadedNovels = <Novel>[];

      for (final key in keys) {
        try {
          final novelData = _novelsBox.get(key);
          print('[DEBUG] 处理键: $key, 数据类型: ${novelData?.runtimeType}');

          if (novelData != null) {
            if (novelData is Novel) {
              print('[DEBUG] 直接加载Novel对象: ${novelData.title}');
              loadedNovels.add(novelData);
            } else if (novelData is Map) {
              print('[DEBUG] 从Map加载Novel: ${novelData['title']}');
              final novel =
                  Novel.fromJson(Map<String, dynamic>.from(novelData));
              loadedNovels.add(novel);
            } else {
              print('[WARNING] 未知数据类型: ${novelData.runtimeType}');
            }
          } else {
            print('[WARNING] 键 $key 对应的数据为null');
          }
        } catch (e, stackTrace) {
          print('[ERROR] 解析小说数据失败，键: $key, 错误: $e');
          print('[ERROR] 错误堆栈: $stackTrace');
        }
      }

      // 按创建时间排序，最新的在前面
      loadedNovels.sort((a, b) => b.createdAt.compareTo(a.createdAt));

      // 更新novels列表
      novels.value = loadedNovels;

      print('[DEBUG] 成功加载 ${loadedNovels.length} 本小说');

      // 打印所有加载的小说标题，用于调试
      for (int i = 0; i < loadedNovels.length; i++) {
        print(
            '[DEBUG] 小说 #${i + 1}: ${loadedNovels[i].title}, 内容长度: ${loadedNovels[i].content.length}字');
      }

      // 强制更新UI
      update();
    } catch (e, stackTrace) {
      print('[ERROR] 加载小说失败: $e');
      print('[ERROR] 错误堆栈: $stackTrace');

      // 如果加载失败，尝试清空列表并重新初始化
      novels.clear();
      update();
    }
  }

  /// 生成小说大纲
  Future<String> generateOutlineFromTitle({
    required String title,
    required String genre,
    required String theme,
    required String targetReaders,
    required int totalChapters,
  }) async {
    try {
      // 使用LangChain服务生成大纲
      final response = await _langchainService.generateOutline(
        novelTitle: title,
        genres: genre.split(','),
        theme: theme,
        targetReaders: targetReaders,
        totalChapters: totalChapters,
        background: background.value,
        otherRequirements: otherRequirements.value,
        writingStyle: selectedWritingStyle.value,
        characterCards: getCompatibleCharacterCards(),
      );
      return response;
    } catch (e) {
      print('生成大纲出错: $e');
      rethrow;
    }
  }

  /// 续写小说 - 检查未生成的章节并补写
  Future<void> continueNovelGeneration() async {
    try {
      // 确保状态正确
      isGenerating.value = true;
      generationStage.value = GenerationStage.generatingChapters;
      generationStatus.value = '正在检查未生成的章节...';

      // 获取当前小说
      final currentNovel = getCurrentNovel();
      if (currentNovel == null || currentOutline.value == null) {
        Get.snackbar('错误', '无法续写，未找到当前小说信息');
        isGenerating.value = false;
        return;
      }

      // 获取大纲中的所有章节
      final outlineChapters = currentOutline.value!.chapters;
      if (outlineChapters.isEmpty) {
        Get.snackbar('提示', '当前小说大纲中没有章节信息');
        isGenerating.value = false;
        return;
      }

      // 获取已生成的章节 - 使用_generatedChapters而不是currentNovel.chapters
      // 因为_generatedChapters在checkAndContinueNovelGeneration中已经被正确初始化
      final generatedChapterNumbers =
          _generatedChapters.map((ch) => ch.number).toSet();

      print('[续写调试] continueNovelGeneration开始');
      print('[续写调试] 大纲中的章节数: ${outlineChapters.length}');
      print(
          '[续写调试] 大纲章节号: ${outlineChapters.map((ch) => ch.chapterNumber).toList()}');
      print('[续写调试] 已生成章节数: ${_generatedChapters.length}');
      print('[续写调试] 已生成章节号: ${generatedChapterNumbers.toList()}');

      // 找出未生成的章节
      final missingChapters = <int>[];
      for (final chapter in outlineChapters) {
        if (!generatedChapterNumbers.contains(chapter.chapterNumber)) {
          missingChapters.add(chapter.chapterNumber);
        }
      }

      // 按章节号排序
      missingChapters.sort();

      print('[续写调试] 需要生成的章节: $missingChapters');

      if (missingChapters.isEmpty) {
        Get.snackbar('提示', '所有章节已生成完毕，无需续写');
        isGenerating.value = false;
        return;
      }

      // 显示未生成章节的信息
      _clearRealtimeOutput();
      _updateRealtimeOutput('检测到以下未生成的章节：\n');
      for (final chapterNumber in missingChapters) {
        final chapterOutline = outlineChapters.firstWhere(
          (ch) => ch.chapterNumber == chapterNumber,
        );
        _updateRealtimeOutput(
            '- 第${chapterNumber}章：${chapterOutline.chapterTitle}\n');
      }
      _updateRealtimeOutput('\n开始续写未生成的章节...\n\n');

      // 开始生成未生成的章节
      final totalChaptersToGenerate = missingChapters.length;
      generationProgress.value = 0.0;

      for (int i = 0; i < missingChapters.length; i++) {
        // 检查是否需要停止
        if (_shouldStop.value) {
          generationStatus.value = '已停止';
          isGenerating.value = false;
          print('生成已停止');
          return;
        }

        // 检查暂停状态并等待
        await _checkPauseState();

        final chapterNumber = missingChapters[i];
        _currentChapter.value = chapterNumber;
        final status = '正在生成第 ${_currentChapter.value} 章...';
        generationStatus.value = status;
        _updateRealtimeOutput('\n-- $status --\n');

        // 调用生成章节的方法
        await generateChapterFromOutline(
          chapterNumber: chapterNumber,
          onStatus: (s) => generationStatus.value = s,
          novelTitle: currentNovel.title,
          sessionId: currentSessionId,
        );

        // 更新进度
        generationProgress.value = (i + 1) / totalChaptersToGenerate;

        // 短暂延时，避免UI卡顿和API调用过于频繁
        await Future.delayed(const Duration(milliseconds: 500));
      }

      generationStatus.value = '续写完成！';
      _updateRealtimeOutput('\n\n所有未生成章节已补写完成！');

      // 设置生成完成状态
      generationStage.value = GenerationStage.generationComplete;

      // 所有章节已在生成过程中实时保存到书库中
      Get.snackbar('续写成功', '小说《${currentNovel.title}》的未生成章节已补写完成并保存到书库中');
    } catch (e) {
      generationStatus.value = '续写失败: $e';
      _updateRealtimeOutput('\n\n续写失败: $e');
      print('续写小说时出错: $e');
      Get.snackbar('续写错误', '续写过程中发生错误: $e');
    } finally {
      isGenerating.value = false;
      isPaused.value = false; // 确保结束时重置暂停状态
      _currentChapter.value = 0; // 重置当前章节
    }
  }

  /// 从大纲中生成章节内容
  Future<Chapter> generateChapterFromOutline({
    required int chapterNumber,
    String? outlineString,
    Function(String)? onStatus,
    String? novelTitle,
    String? sessionId,
  }) async {
    try {
      // 检查每日章节生成限制
      final dailyLimitService = Get.find<DailyLimitService>();
      if (!dailyLimitService.checkAndShowLimitWarning()) {
        throw Exception('已达到每日章节生成限制');
      }

      onStatus?.call('正在生成第$chapterNumber章...');

      // 处理大纲信息
      String chapterTitle = "第$chapterNumber章";
      String chapterOutlineContent = "";
      String title = novelTitle ?? this.title.value;
      List<String> genres = selectedGenres.toList();

      if (outlineString != null) {
        // 从提供的大纲字符串中提取章节信息
        final chapterPattern = RegExp(
            r'第' + chapterNumber.toString() + r'章[:：](.*?)\n(.*?)(?=第\d+章|$)',
            dotAll: true);
        final match = chapterPattern.firstMatch(outlineString);

        if (match != null) {
          chapterTitle = match.group(1)?.trim() ?? chapterTitle;
          chapterOutlineContent = match.group(2)?.trim() ?? "";
        }
      } else if (currentOutline.value != null) {
        // 从当前大纲对象中获取章节信息
        final chapterOutline = currentOutline.value!.chapters.firstWhereOrNull(
          (ch) => ch.chapterNumber == chapterNumber,
        );

        if (chapterOutline != null) {
          chapterTitle = chapterOutline.chapterTitle;
          chapterOutlineContent = chapterOutline.contentOutline;
        }
      }

      // 根据生成模式选择不同的生成服务
      String content;
      // 使用传入的sessionId或当前会话ID
      final String currentSessionIdToUse = sessionId ?? currentSessionId;

      if (generationMode.value == GenerationMode.lightweight) {
        // 使用精简生成服务
        content = await _lightweightService.generateChapter(
          novelTitle: title,
          chapterNumber: chapterNumber,
          chapterTitle: chapterTitle,
          outlineContent: chapterOutlineContent,
          genres: genres,
          theme: background.value,
          targetReaders: targetReader.value,
          background: background.value,
          otherRequirements: otherRequirements.value,
          writingStyle: selectedWritingStyle.value,
          characterCards: getCompatibleCharacterCards(),
          onProgress: (chunk) {
            _updateRealtimeOutput(chunk);
          },
        );

        // 在精简模式下，将细纲保存到NovelMemory
        try {
          final novelMemory =
              NovelMemory(novelTitle: title, sessionId: currentSessionIdToUse);
          await novelMemory.saveChapter(
              chapterNumber, chapterTitle, chapterOutlineContent);
          print('已将第$chapterNumber章细纲保存到NovelMemory');
        } catch (e) {
          print('保存细纲到NovelMemory失败: $e');
        }
      } else {
        // 使用标准生成服务
        content = await _langchainService.generateChapter(
          novelTitle: title,
          chapterNumber: chapterNumber,
          chapterTitle: chapterTitle,
          outlineContent: chapterOutlineContent,
          genres: genres,
          theme: background.value,
          targetReaders: targetReader.value,
          background: background.value,
          otherRequirements: otherRequirements.value,
          writingStyle: selectedWritingStyle.value,
          characterCards: getCompatibleCharacterCards(),
          sessionId: currentSessionIdToUse, // 传入会话ID
          onProgress: (chunk) {
            _updateRealtimeOutput(chunk);
          },
        );
      }

      // 创建章节对象
      final chapter = Chapter(
        number: chapterNumber,
        title: chapterTitle,
        content: content,
      );

      // 保存到已生成章节列表
      final existingIndex =
          _generatedChapters.indexWhere((ch) => ch.number == chapterNumber);
      if (existingIndex >= 0) {
        print('[续写调试] 更新现有章节: 第${chapterNumber}章');
        _generatedChapters[existingIndex] = chapter;
      } else {
        print('[续写调试] 添加新章节: 第${chapterNumber}章');
        _generatedChapters.add(chapter);
        // 确保章节按顺序排序
        _generatedChapters.sort((a, b) => a.number.compareTo(b.number));
      }

      print('[续写调试] 当前_generatedChapters章节数: ${_generatedChapters.length}');
      print(
          '[续写调试] 当前_generatedChapters章节号: ${_generatedChapters.map((ch) => ch.number).toList()}');

      // 保存到Hive
      await _saveChapterToHive(title, chapter);

      // 立即更新小说对象
      await _updateNovelWithCurrentChapters();

      // 增加每日章节计数
      await dailyLimitService.incrementChapterCount();

      onStatus?.call('第$chapterNumber章生成完成');
      return chapter;
    } catch (e) {
      onStatus?.call('生成失败: $e');
      rethrow;
    }
  }

  /// 获取字符串的最后 N 个字符
  String _getLastNChars(String text, int n) {
    if (text.length <= n) {
      return text;
    }
    return text.substring(text.length - n);
  }

  // 修改重置生成状态的方法
  void _resetGenerationState() {
    isGenerating.value = false;
    isPaused.value = false;
    generationStatus.value = '';
  }

  // 添加 _saveChapterToHive 方法
  Future<void> _saveChapterToHive(String novelTitle, Chapter chapter) async {
    try {
      // 获取已经保存的章节列表
      final chaptersKey = 'chapters_$novelTitle';
      List<dynamic> savedChapters =
          _chaptersBox.get(chaptersKey, defaultValue: []) ?? [];

      // 检查是否存在相同编号的章节
      final index = savedChapters.indexWhere((ch) {
        if (ch is Chapter) {
          return ch.number == chapter.number;
        } else if (ch is Map) {
          return ch['number'] == chapter.number;
        }
        return false;
      });

      // 更新或添加章节
      if (index != -1) {
        savedChapters[index] = chapter;
      } else {
        savedChapters.add(chapter);
      }

      // 保存更新后的章节列表
      await _chaptersBox.put(chaptersKey, savedChapters);

      print('保存章节到 Hive 成功: 第${chapter.number}章 - ${chapter.title}');
    } catch (e) {
      print('保存章节到 Hive 失败: $e');
      rethrow;
    }
  }

  // 添加更新小说大纲的方法
  Future<void> updateNovelOutline(
      String novelTitle, String updatedOutline) async {
    try {
      // 查找现有小说
      var novel = novels.firstWhere(
        (n) => n.title == novelTitle,
        orElse: () => throw Exception('找不到小说: $novelTitle'),
      );

      // 创建更新后的小说副本
      var updatedNovel = novel.copyWith(outline: updatedOutline);

      // 在novels列表中更新
      int index = novels.indexWhere((n) => n.title == novelTitle);
      if (index != -1) {
        novels[index] = updatedNovel;
      }

      // 保存到存储
      await _saveToHive(updatedNovel);

      print('小说大纲更新成功: $novelTitle');
    } catch (e) {
      print('更新小说大纲失败: $e');
      rethrow;
    }
  }

  String _buildGenerationPrompt() {
    final buffer = StringBuffer();

    // 添加文风包提示
    final writingStylePrompt = getWritingStylePrompt();
    if (writingStylePrompt.isNotEmpty) {
      buffer.writeln(writingStylePrompt);
      buffer.writeln();
    }

    // 添加其他提示
    buffer.writeln('''
请根据以下要求创作小说：

标题：$title
类型：${selectedGenres.join('、')}
目标读者：$targetReader
写作风格：$style
''');

    if (background.isNotEmpty) {
      buffer.writeln('故事背景：$background');
    }

    if (otherRequirements.isNotEmpty) {
      buffer.writeln('其他要求：$otherRequirements');
    }

    if (selectedCharacterTypes.isNotEmpty) {
      buffer.writeln('\n角色设定：');
      for (final type in selectedCharacterTypes) {
        final cards = selectedCharacterCards[type.id];
        if (cards != null && cards.isNotEmpty) {
          buffer.writeln('${type.name}：');
          for (final card in cards) {
            buffer.writeln('''
- 姓名：${card.name}
- 性别：${card.gender ?? '未设定'}
- 年龄：${card.age ?? '未设定'}
- 性格：${card.personalityTraits ?? '未设定'}
- 背景：${card.background ?? '未设定'}
''');
          }
        }
      }
    }

    return buffer.toString();
  }

  // Wrapper for generating the initial outline (Now displays human-readable text)
  Future<void> generateOutlineWrapper() async {
    if (isGenerating.value || generationStage.value != GenerationStage.idle) {
      Get.snackbar('提示', '正在处理中，请稍候...');
      return;
    }
    if (title.value.isEmpty) {
      Get.snackbar('提示', '请输入故事标题');
      return;
    }

    isGenerating.value = true;
    generationStage.value = GenerationStage.generatingOutline;
    generationStatus.value = '正在生成大纲...'; // Simplified status
    _clearRealtimeOutput();

    // 检查是否是续写模式 - 如果currentOutline已存在，说明是从书库中选择的小说进行续写
    bool isContinuation = currentOutline.value != null;

    // 如果是续写模式，保留当前大纲，否则清空
    if (!isContinuation) {
      currentOutline.value = null;
      rawGeneratedOutlineText.value = '';
    } else {
      _updateRealtimeOutput("检测到续写模式，将基于现有大纲扩展...\n");
      _updateRealtimeOutput(
          "当前大纲包含 ${currentOutline.value!.chapters.length} 章，目标章节数: $_totalChapters\n\n");
    }

    try {
      // 1. Get the JSON array string of chapters from the service
      // 检查是否是续写模式
      bool isContinuation = currentOutline.value != null;
      int existingChaptersCount =
          isContinuation ? currentOutline.value!.chapters.length : 0;

      // 初始化chaptersJsonArrayString
      String chaptersJsonArrayString = "";

      if (isContinuation && existingChaptersCount >= _totalChapters.value) {
        // 如果已有章节数大于等于目标章节数，无需生成新大纲
        _updateRealtimeOutput("当前大纲章节数已满足目标章节数，无需扩展大纲。\n");
        generationStage.value = GenerationStage.outlineReady;
        chaptersJsonArrayString = jsonEncode(currentOutline.value);
      } else if (isContinuation) {
        // 续写模式 - 只生成后续章节的大纲
        _updateRealtimeOutput(
            "续写模式：基于现有大纲扩展，从第${existingChaptersCount + 1}章开始，扩展到第${_totalChapters.value}章...\n");

        // 获取已有大纲的JSON字符串
        String existingOutlineJson = jsonEncode(currentOutline.value);
        _updateRealtimeOutput("已加载现有大纲，包含 $existingChaptersCount 章\n");

        // 检查嵌入模型是否启用
        final apiConfigController = Get.find<ApiConfigController>();
        final vectorizationService = Get.find<NovelVectorizationService>();
        bool embeddingEnabled =
            apiConfigController.embeddingModel.value.enabled;

        // 构建提示词，明确指出这是续写
        String continuationPrompt =
            "这是一个续写任务。请基于已有的$existingChaptersCount章内容，为《${title.value}》生成从第${existingChaptersCount + 1}章到第${_totalChapters.value}章的大纲。保持与已有内容的连贯性和一致性。";

        // 添加明确的章节编号要求
        continuationPrompt += "\n\n重要要求：";
        continuationPrompt +=
            "\n1. 必须生成连续的章节编号，从第${existingChaptersCount + 1}章开始，到第${_totalChapters.value}章结束";
        continuationPrompt += "\n2. 不要跳过任何章节编号";
        continuationPrompt += "\n3. 每个章节都必须有明确的chapterNumber字段";
        continuationPrompt +=
            "\n4. 总共需要生成 ${_totalChapters.value - existingChaptersCount} 个新章节";

        // 添加已有大纲信息
        continuationPrompt += "\n\n现有大纲信息：\n$existingOutlineJson";

        // 如果嵌入模型启用，获取相关内容
        String relevantContent = "";
        if (embeddingEnabled) {
          _updateRealtimeOutput("嵌入模型已启用，正在获取相关内容...\n");

          // 确保小说已向量化
          if (!vectorizationService.isNovelVectorized(title.value)) {
            _updateRealtimeOutput("小说未向量化，正在向量化...\n");
            await vectorizationService.vectorizeNovel(title.value);
          }

          // 构建优化的续写大纲查询文本
          String queryText = _buildOutlineContinuationQueryText(
            novelTitle: title.value,
            startChapter: existingChaptersCount + 1,
            endChapter: _totalChapters.value,
            genres: selectedGenres.toList(),
            theme: background.value,
            targetReader: targetReader.value,
          );

          // 获取相关内容
          final searchResults = await vectorizationService.searchNovelContent(
            title.value,
            queryText,
            maxResults: apiConfigController.embeddingModel.value.topK,
            includeKnowledgeBase: true,
            includeWritingStyle: true,
          );

          if (searchResults.isNotEmpty) {
            _updateRealtimeOutput("找到 ${searchResults.length} 条相关内容\n");

            // 构建相关内容字符串
            StringBuffer contentBuffer = StringBuffer();
            contentBuffer.writeln("\n\n相关章节内容：");

            for (final result in searchResults) {
              final type = result['type'] as String;
              final content = result['content'] as String;
              final similarity = result['similarity'] as double;

              if (type == 'chapter') {
                final chapterNumber = result['chapter'] as int;
                final chapterTitle = result['title'] as String;

                contentBuffer.writeln(
                    "- 第$chapterNumber章 $chapterTitle (相似度: ${similarity.toStringAsFixed(2)})");
                contentBuffer.writeln("$content\n");
              }
            }

            relevantContent = contentBuffer.toString();
          } else {
            _updateRealtimeOutput("未找到相关内容\n");
          }
        }

        // 将相关内容添加到提示词中
        if (relevantContent.isNotEmpty) {
          continuationPrompt += relevantContent;
        }

        // 根据生成模式选择不同的生成服务
        if (generationMode.value == GenerationMode.lightweight) {
          // 使用精简生成服务
          _updateRealtimeOutput("使用精简生成模式扩展大纲...\n");
          chaptersJsonArrayString = await _lightweightService.generateOutline(
            novelTitle: title.value,
            genres: selectedGenres.toList(),
            theme: background.value,
            targetReaders: targetReader.value,
            totalChapters: _totalChapters.value,
            background: background.value,
            otherRequirements:
                "${otherRequirements.value}\n$continuationPrompt",
            writingStyle: selectedWritingStyle.value,
            characterCards: getCompatibleCharacterCards(),
          );
        } else {
          // 使用标准生成服务
          _updateRealtimeOutput("使用标准生成模式扩展大纲...\n");
          chaptersJsonArrayString = await _langchainService.generateOutline(
            novelTitle: title.value,
            genres: selectedGenres.toList(),
            theme: background.value,
            targetReaders: targetReader.value,
            totalChapters: _totalChapters.value,
            background: background.value,
            otherRequirements:
                "${otherRequirements.value}\n$continuationPrompt",
            writingStyle: selectedWritingStyle.value,
            characterCards: getCompatibleCharacterCards(),
          );
        }
      } else {
        // 新小说模式 - 生成完整大纲
        // 根据生成模式选择不同的生成服务
        if (generationMode.value == GenerationMode.lightweight) {
          // 使用精简生成服务
          _updateRealtimeOutput("使用精简生成模式生成大纲...\n");
          chaptersJsonArrayString = await _lightweightService.generateOutline(
            novelTitle: title.value,
            genres: selectedGenres.toList(),
            theme: background.value,
            targetReaders: targetReader.value,
            totalChapters: totalChapters,
            background: background.value,
            otherRequirements: otherRequirements.value,
            writingStyle: selectedWritingStyle.value,
            characterCards: getCompatibleCharacterCards(),
            onRealtimeOutput: _updateRealtimeOutput,
          );
        } else {
          // 使用标准生成服务
          _updateRealtimeOutput("使用标准生成模式生成大纲...\n");
          chaptersJsonArrayString = await _langchainService.generateOutline(
            novelTitle: title.value,
            genres: selectedGenres.toList(),
            theme: background.value,
            targetReaders: targetReader.value,
            totalChapters: totalChapters,
            background: background.value,
            otherRequirements: otherRequirements.value,
            writingStyle: selectedWritingStyle.value,
            characterCards: getCompatibleCharacterCards(),
            onRealtimeOutput: _updateRealtimeOutput,
          );
        }
      }

      print("--- AI Raw Outline Output (expecting JSON Array String) ---");
      print(chaptersJsonArrayString);
      print("----------------------------------------------");

      // 2. Parse the JSON string (could be array or object)
      List<dynamic> chaptersList = [];
      try {
        var decodedJson = jsonDecode(chaptersJsonArrayString);

        // 处理不同的JSON格式
        if (decodedJson is List<dynamic>) {
          // 直接使用数组格式
          chaptersList = decodedJson;
        } else if (decodedJson is Map<String, dynamic>) {
          // 如果是对象格式，提取chapters字段
          print("检测到对象格式的JSON，提取chapters字段");
          if (decodedJson.containsKey('chapters') &&
              decodedJson['chapters'] is List) {
            chaptersList = decodedJson['chapters'] as List<dynamic>;
          } else {
            throw Exception("对象格式的JSON中缺少有效的chapters字段");
          }
        } else {
          throw Exception("无法识别的JSON格式，既不是数组也不是对象");
        }
      } catch (e) {
        print("错误：无法解析从服务返回的JSON: $e");
        print("接收到的字符串: $chaptersJsonArrayString");
        throw Exception("生成的大纲格式无效，无法解析章节列表。");
      }

      // 3. Format the parsed list into human-readable text for UI
      final readableOutlineBuffer = StringBuffer();
      final chapterOutlineObjects = <ChapterOutline>[];

      // 在续写模式下，验证章节编号的连续性
      if (isContinuation) {
        _updateRealtimeOutput("续写模式：验证生成的章节编号...\n");
        print(
            "[续写调试] 期望的章节范围: ${existingChaptersCount + 1} 到 ${_totalChapters.value}");
        print("[续写调试] 生成的章节数据: ${chaptersList.length} 个");
      }

      for (var chapterData in chaptersList) {
        if (chapterData is Map<String, dynamic>) {
          final chapterNumber = chapterData['chapterNumber'] as int? ?? 0;
          final chapterTitle = chapterData['chapterTitle'] as String? ?? '无标题';
          final summary = chapterData['summary'] as String? ??
              '无摘要'; // Assuming key is 'summary' based on previous examples

          // 在续写模式下，验证章节编号是否在期望范围内
          if (isContinuation) {
            if (chapterNumber <= existingChaptersCount) {
              print(
                  "[续写调试] 警告：生成的章节号 $chapterNumber 小于等于已有章节数 $existingChaptersCount，跳过");
              _updateRealtimeOutput("跳过重复章节：第$chapterNumber章\n");
              continue;
            }
            if (chapterNumber > _totalChapters.value) {
              print(
                  "[续写调试] 警告：生成的章节号 $chapterNumber 超过目标章节数 ${_totalChapters.value}，跳过");
              _updateRealtimeOutput("跳过超出范围的章节：第$chapterNumber章\n");
              continue;
            }
            print("[续写调试] 接受章节：第$chapterNumber章 - $chapterTitle");
          }

          readableOutlineBuffer.writeln("第$chapterNumber章：$chapterTitle");
          readableOutlineBuffer.writeln(summary);
          readableOutlineBuffer.writeln(); // Add a blank line between chapters

          // Create ChapterOutline object for internal storage
          chapterOutlineObjects.add(ChapterOutline(
            chapterNumber: chapterNumber,
            chapterTitle: chapterTitle,
            // Assuming ChapterOutline has contentOutline, initialize it empty or with summary?
            contentOutline:
                summary, // For now, let's put summary here as initial content
            // chapterSummary: summary, // If there's a dedicated field
          ));
        } else {
          print("警告：解析出的章节数据格式无效: $chapterData");
        }
      }

      // 在续写模式下，检查是否有缺失的章节
      if (isContinuation && chapterOutlineObjects.isNotEmpty) {
        final generatedChapterNumbers =
            chapterOutlineObjects.map((ch) => ch.chapterNumber).toSet();
        final expectedChapterNumbers = List.generate(
            _totalChapters.value - existingChaptersCount,
            (index) => existingChaptersCount + 1 + index).toSet();

        final missingChapters =
            expectedChapterNumbers.difference(generatedChapterNumbers);
        if (missingChapters.isNotEmpty) {
          _updateRealtimeOutput(
              "警告：以下章节未生成: ${missingChapters.toList()..sort()}\n");
          print("[续写调试] 缺失章节: $missingChapters");
        }

        _updateRealtimeOutput("成功生成 ${chapterOutlineObjects.length} 个新章节\n");
      }

      // 4. Update UI with the readable text
      final readableText = readableOutlineBuffer.toString().trim();
      rawGeneratedOutlineText.value = readableText;
      _updateRealtimeOutput(
          "--- 生成的大纲 (预览) ---\n$readableText\n----------------\n");

      // 5. Store the structured outline internally
      if (isContinuation) {
        // 续写模式：将新章节添加到现有大纲中
        final existingChapters =
            List<ChapterOutline>.from(currentOutline.value!.chapters);

        // 添加新生成的章节到现有章节列表
        for (final newChapter in chapterOutlineObjects) {
          // 检查章节是否已存在，如果不存在则添加
          if (!existingChapters
              .any((ch) => ch.chapterNumber == newChapter.chapterNumber)) {
            existingChapters.add(newChapter);
          }
        }

        // 按章节号排序
        existingChapters
            .sort((a, b) => a.chapterNumber.compareTo(b.chapterNumber));

        currentOutline.value = NovelOutline(
          novelTitle: title.value,
          chapters: existingChapters,
        );

        _updateRealtimeOutput(
            "已将新章节添加到现有大纲中，当前总章节数: ${existingChapters.length}\n");
      } else {
        // 新小说模式：直接使用生成的章节
        currentOutline.value = NovelOutline(
          novelTitle: title.value,
          chapters: chapterOutlineObjects,
        );
      }

      _totalChapters.value = currentOutline.value!.chapters.length; // 使用实际的章节数量
      isUsingOutline.value = true; // Mark that we have a structured outline

      generationStatus.value = '大纲已生成，请确认。';
      generationStage.value = GenerationStage.outlineReady;
      _updateRealtimeOutput(
          "\n大纲已生成。请预览上方内容，然后点击 \"生成细纲\"。\n(注意：编辑框中的文本仅供预览，修改无效。细纲将基于内部存储的大纲生成)");
    } catch (e) {
      generationStatus.value = '生成大纲失败: $e';
      generationStage.value = GenerationStage.error;
      print('生成大纲时出错: $e');
      Get.snackbar('生成错误', '生成大纲时发生错误: $e');
      _updateRealtimeOutput('\n生成大纲时出错: $e');
    } finally {
      isGenerating.value = false;
    }
  }

  // Stage 2: Generate detailed outline (Now uses internal currentOutline.value)
  Future<void> generateDetailedOutlineFromEdited(
      String editedOutlineText /* This parameter is now ignored */) async {
    // Remove check for generation stage or adjust if needed
    // if (generationStage.value != GenerationStage.outlineReady) { ... }

    if (isGenerating.value) {
      Get.snackbar('提示', '正在处理中，请稍候...');
      return;
    }

    // --- Use the internally stored currentOutline.value ---
    if (currentOutline.value == null) {
      generationStage.value = GenerationStage.idle; // Or error?
      Get.snackbar('错误', '内部大纲数据不存在！请先生成大纲。');
      _updateRealtimeOutput('\n错误：找不到用于生成细纲的内部大纲数据。请重新生成大纲。');
      return;
    }

    // --- Removed parsing logic for editedOutlineText ---
    // NovelOutline? reParsedOutline = currentOutline.value; // Directly use internal state
    // No need to update currentOutline again here, it's already set

    // 2. Start generating detailed outline
    isGenerating.value = true;
    generationStage.value = GenerationStage.generatingDetailedOutline;
    generationStatus.value = '正在生成章节细纲...';
    _clearRealtimeOutput();
    _updateRealtimeOutput('--- 正在根据内部存储的大纲生成细纲 ---\n'); // Updated message

    try {
      // 3. Call LangChain service - Loop through chapters (using currentOutline.value)
      if (currentOutline.value!.chapters.isEmpty) {
        // Null check already done above
        throw Exception("当前内部大纲没有章节。");
      }

      // No need for chaptersToProcess copy if we update the original list via index
      final totalChaptersToProcess = currentOutline.value!.chapters.length;

      // 检查是否是续写模式 - 如果已有章节，说明是续写模式
      bool isContinuation = _generatedChapters.isNotEmpty;
      int existingChaptersCount =
          isContinuation ? _generatedChapters.length : 0;

      if (isContinuation) {
        _updateRealtimeOutput("检测到续写模式，已有 $existingChaptersCount 章内容\n");
        _updateRealtimeOutput(
            "开始为剩余 ${totalChaptersToProcess - existingChaptersCount} 章生成细纲...\n\n");
      } else {
        _updateRealtimeOutput("开始为 $totalChaptersToProcess 章生成细纲...\n\n");
      }

      // 检查嵌入模型是否启用
      final apiConfigController = Get.find<ApiConfigController>();
      final vectorizationService = Get.find<NovelVectorizationService>();
      bool embeddingEnabled = apiConfigController.embeddingModel.value.enabled;

      // 如果嵌入模型启用，确保小说已向量化
      if (embeddingEnabled && isContinuation) {
        if (!vectorizationService.isNovelVectorized(title.value)) {
          _updateRealtimeOutput("小说未向量化，正在向量化...\n");
          await vectorizationService.vectorizeNovel(title.value);
        }
      }

      for (int i = 0; i < totalChaptersToProcess; i++) {
        // Directly access chapter from the reactive list
        final chapter = currentOutline.value!.chapters[i];
        final chapterNumber = chapter.chapterNumber;
        final chapterTitle = chapter.chapterTitle;

        // 如果是续写模式，跳过已有章节的细纲生成
        if (isContinuation && chapterNumber <= existingChaptersCount) {
          _updateRealtimeOutput("\n跳过第 $chapterNumber 章《$chapterTitle》，已有内容");
          continue;
        }

        generationStatus.value =
            '正在生成第 $chapterNumber 章 / 共 $totalChaptersToProcess 章的细纲...';
        _updateRealtimeOutput('\n正在生成第 $chapterNumber 章《$chapterTitle》的细纲...');

        // 准备额外的上下文信息
        String additionalContext = "";

        // 如果是续写模式且嵌入模型启用，获取相关内容
        if (isContinuation && embeddingEnabled) {
          // 构建优化的续写查询文本
          String queryText = _buildContinuationQueryText(
            novelTitle: title.value,
            chapterNumber: chapterNumber,
            chapterTitle: chapterTitle,
            chapterOutline: chapter.contentOutline,
            genres: selectedGenres.toList(),
            theme: background.value,
          );

          // 获取相关内容
          final searchResults = await vectorizationService.searchNovelContent(
            title.value,
            queryText,
            maxResults: apiConfigController.embeddingModel.value.topK,
            includeKnowledgeBase: true,
            includeWritingStyle: true,
          );

          if (searchResults.isNotEmpty) {
            _updateRealtimeOutput("找到 ${searchResults.length} 条相关内容");

            // 构建相关内容字符串
            StringBuffer contentBuffer = StringBuffer();
            contentBuffer.writeln("\n相关章节内容：");

            for (final result in searchResults) {
              final type = result['type'] as String;
              final content = result['content'] as String;

              if (type == 'chapter') {
                final resultChapterNumber = result['chapter'] as int;
                final resultChapterTitle = result['title'] as String;

                contentBuffer
                    .writeln("- 第$resultChapterNumber章 $resultChapterTitle");
                contentBuffer.writeln("$content\n");
              }
            }

            additionalContext = contentBuffer.toString();
          }
        }

        try {
          String detailedMarkdownOutline;

          // 构建提示词
          String promptAddition = "";
          if (isContinuation) {
            promptAddition = "这是一个续写任务，请确保细纲与已有章节内容保持连贯性和一致性。";
            if (additionalContext.isNotEmpty) {
              promptAddition += additionalContext;
            }
          }

          // 根据生成模式选择不同的生成服务
          if (generationMode.value == GenerationMode.lightweight) {
            // 使用精简生成服务
            detailedMarkdownOutline =
                await _lightweightService.generateDetailedChapterOutline(
              novelTitle: currentOutline.value!.novelTitle,
              chapterNumber: chapterNumber,
              chapterTitle: currentOutline.value!.chapters[i].chapterTitle,
              chapterSummary: currentOutline.value!.chapters[i].contentOutline,
              genres: selectedGenres.toList(),
              theme: background.value,
              targetReaders: targetReader.value,
              background: background.value,
              otherRequirements: promptAddition.isNotEmpty
                  ? "${otherRequirements.value}\n\n$promptAddition"
                  : otherRequirements.value,
              writingStyle: selectedWritingStyle.value,
              characterCards: getCompatibleCharacterCards(),
            );
          } else {
            // 使用标准生成服务
            detailedMarkdownOutline =
                await _langchainService.generateDetailedChapterOutline(
              novelTitle: currentOutline.value!.novelTitle,
              chapterNumber: chapterNumber,
              genres: selectedGenres.toList(),
              theme: background.value,
              targetReaders: targetReader.value,
              background: background.value,
              otherRequirements: promptAddition.isNotEmpty
                  ? "${otherRequirements.value}\n\n$promptAddition"
                  : otherRequirements.value,
              writingStyle: selectedWritingStyle.value,
              characterCards: getCompatibleCharacterCards(),
            );
          }

          // Find the corresponding chapter index (should always be `i` now)
          // FIX: Create a new ChapterOutline instance with the updated contentOutline
          final oldChapter = currentOutline.value!.chapters[i];
          final newChapter = ChapterOutline(
            chapterNumber: oldChapter.chapterNumber,
            chapterTitle: oldChapter.chapterTitle,
            // Keep the original summary/contentOutline from the first stage if needed,
            // or just update the detail here.
            contentOutline:
                detailedMarkdownOutline, // Use the new detailed outline
          );
          // Update the list directly - GetX should handle reactivity
          currentOutline.value!.chapters[i] = newChapter;

          _updateRealtimeOutput('第 $chapterNumber 章细纲生成完毕。');
          // No need to call currentOutline.refresh() explicitly for List updates usually
          // If UI doesn't update, uncomment the refresh() call
          // currentOutline.refresh();
        } catch (e) {
          generationStatus.value = '生成第 $chapterNumber 章细纲失败: $e';
          generationStage.value = GenerationStage.error;
          print('生成第 $chapterNumber 章细纲时出错: $e');
          Get.snackbar('生成错误', '生成第 $chapterNumber 章细纲时发生错误: $e');
          _updateRealtimeOutput('\n生成第 $chapterNumber 章细纲时出错: $e\n停止后续生成。');
          isGenerating.value = false;
          return; // Stop processing further chapters on error
        }
      }

      // 4. Update final state after loop completion
      generationStatus.value = '所有章节细纲已生成，请确认。';
      generationStage.value = GenerationStage.detailedOutlineReady;
      _updateRealtimeOutput('''\n--- 所有章节细纲生成完成 ---
请确认下方细纲，然后点击"生成小说章节"。\n''');
      // Display the final outline with all detailed content
      _displayDetailedOutlineInOutput(currentOutline.value!);

      // 将细纲保存为第0章
      await _saveOutlineAsChapterZero();
    } catch (e) {
      generationStatus.value = '生成细纲失败: $e';
      generationStage.value = GenerationStage.error;
      print('生成细纲时出错: $e');
      Get.snackbar('生成错误', '生成细纲时发生错误: $e');
      _updateRealtimeOutput('\n生成细纲时出错: $e');
    } finally {
      // Ensure isGenerating is set to false
      isGenerating.value = false;
    }
  }

  // Helper to display detailed outline (optional)
  void _displayDetailedOutlineInOutput(NovelOutline detailedOutline) {
    final buffer = StringBuffer();
    buffer.writeln("\n--- 生成的细纲 ---");
    for (final chapter in detailedOutline.chapters) {
      buffer
          .writeln("\n**第${chapter.chapterNumber}章: ${chapter.chapterTitle}**");
      buffer.writeln(
          chapter.contentOutline); // This now contains the detailed plot
    }
    buffer.writeln("\n-----------------");
    _updateRealtimeOutput(buffer.toString());
  }

  // 将细纲保存为第0章
  Future<void> _saveOutlineAsChapterZero() async {
    try {
      if (currentOutline.value == null ||
          currentOutline.value!.chapters.isEmpty) {
        // 无法保存细纲：细纲为空
        return;
      }

      // 构建细纲内容
      final buffer = StringBuffer();
      buffer.writeln('小说标题：${currentOutline.value!.novelTitle}');
      buffer.writeln('生成时间：${DateTime.now().toString()}');
      buffer.writeln('总章节数：${currentOutline.value!.chapters.length}');
      buffer.writeln();

      for (final chOutline in currentOutline.value!.chapters) {
        buffer
            .writeln('第${chOutline.chapterNumber}章：${chOutline.chapterTitle}');
        buffer.writeln(chOutline.contentOutline);
        buffer.writeln();
      }

      final outlineContent = buffer.toString();

      // 创建第0章
      final outlineChapter = Chapter(
        number: 0,
        title: '细纲',
        content: outlineContent,
      );

      // 保存到已生成章节列表
      final existingIndex =
          _generatedChapters.indexWhere((ch) => ch.number == 0);
      if (existingIndex >= 0) {
        _generatedChapters[existingIndex] = outlineChapter;
      } else {
        _generatedChapters.add(outlineChapter);
        // 确保章节按顺序排序
        _generatedChapters.sort((a, b) => a.number.compareTo(b.number));
      }

      // 保存到Hive
      await _saveChapterToHive(title.value, outlineChapter);

      // 创建或更新小说对象
      await _updateNovelWithCurrentChapters();

      // 同时保存每个章节的细纲到NovelMemory
      try {
        // 使用标准化的小说标题
        final normalizedTitle = title.value.trim();
        final sessionId = _getOrCreateSessionId(normalizedTitle);

        // 1. 使用会话ID保存一份
        final novelMemoryWithSession =
            NovelMemory(novelTitle: normalizedTitle, sessionId: sessionId);

        // 2. 不使用会话ID再保存一份，确保在任何情况下都能找到
        final novelMemoryNoSession = NovelMemory(novelTitle: normalizedTitle);

        // 保存每个章节的细纲 - 两份都保存
        for (final chOutline in currentOutline.value!.chapters) {
          // 保存到带会话ID的存储
          await novelMemoryWithSession.saveChapter(chOutline.chapterNumber,
              chOutline.chapterTitle, chOutline.contentOutline);

          // 保存到不带会话ID的存储
          await novelMemoryNoSession.saveChapter(chOutline.chapterNumber,
              chOutline.chapterTitle, chOutline.contentOutline);
        }

        print('已保存所有章节细纲到NovelMemory，标题: "$normalizedTitle", 会话ID: $sessionId');
      } catch (e) {
        // 即使保存到NovelMemory失败，也不影响保存到Hive
        print('保存章节细纲到NovelMemory失败: $e');
      }
    } catch (e) {
      // 保存细纲为第0章时出错
    }
  }

  // 使用当前生成的章节更新小说
  Future<void> _updateNovelWithCurrentChapters() async {
    try {
      if (_generatedChapters.isEmpty) {
        return;
      }

      // 构建大纲字符串
      String outlineString = '';
      if (currentOutline.value != null) {
        final buffer = StringBuffer();
        buffer.writeln('小说标题：${currentOutline.value!.novelTitle}');
        for (final chOutline in currentOutline.value!.chapters) {
          buffer.writeln(
              '第${chOutline.chapterNumber}章：${chOutline.chapterTitle}');
          buffer.writeln(chOutline.contentOutline);
          buffer.writeln();
        }
        outlineString = buffer.toString();
      }

      // 查找现有小说，避免重复创建
      var existingNovelIndex = novels.indexWhere((n) => n.title == title.value);
      Novel novelToSave;
      List<Chapter> finalChaptersList;

      if (existingNovelIndex != -1) {
        // 如果已存在同名小说，需要合并章节而不是替换
        final existingNovel = novels[existingNovelIndex];
        final existingChapters = List<Chapter>.from(existingNovel.chapters);

        print('[续写调试] 更新现有小说: ${title.value}');
        print('[续写调试] 现有小说章节数: ${existingChapters.length}');
        print(
            '[续写调试] 现有小说章节号: ${existingChapters.map((ch) => ch.number).toList()}');
        print('[续写调试] _generatedChapters章节数: ${_generatedChapters.length}');
        print(
            '[续写调试] _generatedChapters章节号: ${_generatedChapters.map((ch) => ch.number).toList()}');

        // 创建一个Map来存储所有章节，以章节号为键
        final Map<int, Chapter> allChaptersMap = {};

        // 先添加现有章节
        for (final chapter in existingChapters) {
          allChaptersMap[chapter.number] = chapter;
        }

        // 然后添加或更新新生成的章节
        for (final chapter in _generatedChapters) {
          allChaptersMap[chapter.number] = chapter;
        }

        // 按章节号排序生成最终章节列表
        finalChaptersList = allChaptersMap.values.toList();
        finalChaptersList.sort((a, b) => a.number.compareTo(b.number));

        print('[续写调试] 合并后章节数: ${finalChaptersList.length}');
        print(
            '[续写调试] 合并后章节号: ${finalChaptersList.map((ch) => ch.number).toList()}');

        // 重新计算完整内容
        final novelContent =
            finalChaptersList.map((c) => c.content).join('\n\n');

        // 使用 copyWith 更新现有小说
        novelToSave = existingNovel.copyWith(
          chapters: finalChaptersList, // 使用合并后的章节列表
          content: novelContent, // 更新完整内容
          outline: outlineString, // 更新大纲字符串
          updatedAt: DateTime.now(), // 更新时间
          style: style.value, // 更新风格
        );
      } else {
        // 如果不存在，创建新的 Novel 对象
        finalChaptersList = List<Chapter>.from(_generatedChapters);
        final novelContent =
            finalChaptersList.map((c) => c.content).join('\n\n');

        novelToSave = Novel(
          title: title.value,
          genre: selectedGenres.join(','),
          outline: outlineString, // 保存构建的大纲字符串
          content: novelContent,
          chapters: finalChaptersList,
          createdAt: DateTime.now(), // 使用当前时间
          style: style.value, // 保存风格
        );
      }

      // 调用保存方法
      await saveNovel(novelToSave);
      print('小说已更新: ${novelToSave.title}，共${finalChaptersList.length}章');
    } catch (e) {
      print('更新小说时出错: $e');
    }
  }

  // 短篇小说生成方法（分阶段确认流程）
  Future<void> generateShortNovel() async {
    if (isGenerating.value) {
      Get.snackbar('提示', '正在生成中，请稍候...');
      return;
    }

    try {
      isGenerating.value = true;
      generationStage.value = GenerationStage.generatingShortNovelWorldBuilding;
      _updateRealtimeOutput('开始生成短篇小说...\n');

      // 第一步：生成世界观和角色构建
      await _generateShortNovelWorldBuilding();

      // 生成完成后暂停，等待用户确认
      generationStage.value = GenerationStage.shortNovelWorldBuildingReady;
      isGenerating.value = false;

      Get.snackbar('提示', '世界观已生成，请确认后继续生成大纲。');
    } catch (e, stackTrace) {
      print('[ERROR] 短篇小说世界观生成失败: $e');
      print('[ERROR] 错误堆栈: $stackTrace');

      generationStatus.value = '生成失败: $e';
      generationStage.value = GenerationStage.error;
      _updateRealtimeOutput('\n生成失败: $e');

      Get.snackbar('错误', '短篇小说世界观生成失败: $e');
      isGenerating.value = false;
    }
  }

  // 继续生成短篇小说大纲
  Future<void> continueShortNovelOutlineGeneration() async {
    if (isGenerating.value) {
      Get.snackbar('提示', '正在生成中，请稍候...');
      return;
    }

    try {
      isGenerating.value = true;
      generationStage.value = GenerationStage.generatingShortNovelOutline;

      // 第二步：生成详细大纲
      await _generateShortNovelDetailedOutline();

      // 生成完成后暂停，等待用户确认
      generationStage.value = GenerationStage.shortNovelOutlineReady;
      isGenerating.value = false;

      Get.snackbar('提示', '大纲已生成，请确认后开始生成小说内容。');
    } catch (e, stackTrace) {
      print('[ERROR] 短篇小说大纲生成失败: $e');
      print('[ERROR] 错误堆栈: $stackTrace');

      generationStatus.value = '生成失败: $e';
      generationStage.value = GenerationStage.error;
      _updateRealtimeOutput('\n生成失败: $e');

      Get.snackbar('错误', '短篇小说大纲生成失败: $e');
      isGenerating.value = false;
    }
  }

  // 继续生成短篇小说内容
  Future<void> continueShortNovelContentGeneration() async {
    if (isGenerating.value) {
      Get.snackbar('提示', '正在生成中，请稍候...');
      return;
    }

    try {
      isGenerating.value = true;
      generationStage.value = GenerationStage.generatingShortNovelContent;

      // 第三步：分段生成小说内容
      await _generateShortNovelContent();

      generationStage.value = GenerationStage.generationComplete;
      generationStatus.value = '短篇小说生成完成';
      _updateRealtimeOutput('\n短篇小说生成完成！');

      Get.snackbar('成功', '短篇小说《${title.value}》生成完成！');
    } catch (e, stackTrace) {
      print('[ERROR] 短篇小说内容生成失败: $e');
      print('[ERROR] 错误堆栈: $stackTrace');

      generationStatus.value = '生成失败: $e';
      generationStage.value = GenerationStage.error;
      _updateRealtimeOutput('\n生成失败: $e');

      Get.snackbar('错误', '短篇小说内容生成失败: $e');
    } finally {
      isGenerating.value = false;
    }
  }

  // 显示世界观预览界面
  void showShortNovelWorldBuildingPreview() {
    Get.to(() => ShortNovelWorldBuildingPreviewScreen(
          worldBuilding: shortNovelWorldBuilding.value,
          onWorldBuildingConfirmed: (updatedWorldBuilding) {
            // 更新世界观内容
            shortNovelWorldBuilding.value = updatedWorldBuilding;
            // 保存到记忆系统
            _saveShortNovelWorldBuildingToMemory(updatedWorldBuilding);
            // 继续生成大纲
            continueShortNovelOutlineGeneration();
          },
        ));
  }

  // 显示大纲预览界面
  void showShortNovelOutlinePreview() {
    Get.to(() => ShortNovelOutlinePreviewScreen(
          outline: shortNovelDetailedOutline.value,
          onOutlineConfirmed: (updatedOutline) {
            // 更新大纲内容
            shortNovelDetailedOutline.value = updatedOutline;
            // 保存到记忆系统
            _saveShortNovelOutlineToMemory(updatedOutline);
            // 继续生成小说内容
            continueShortNovelContentGeneration();
          },
        ));
  }

  // 保存世界观到记忆系统
  Future<void> _saveShortNovelWorldBuildingToMemory(
      String worldBuilding) async {
    try {
      final sessionId = _getOrCreateSessionId(title.value);
      final novelMemory =
          NovelMemory(novelTitle: title.value, sessionId: sessionId);
      await novelMemory.saveShortNovelWorldBuilding(worldBuilding);
    } catch (e) {
      print('保存世界观到记忆系统失败: $e');
    }
  }

  // 保存大纲到记忆系统
  Future<void> _saveShortNovelOutlineToMemory(String outline) async {
    try {
      final sessionId = _getOrCreateSessionId(title.value);
      final novelMemory =
          NovelMemory(novelTitle: title.value, sessionId: sessionId);
      await novelMemory.saveShortNovelDetailedOutline(outline);
    } catch (e) {
      print('保存大纲到记忆系统失败: $e');
    }
  }

  // 简化的短篇小说生成方法
  Future<void> _generateShortNovelSimple() async {
    _updateRealtimeOutput('正在生成短篇小说内容...\n');
    generationStatus.value = '正在生成短篇小说内容...';

    try {
      final totalWordCount = shortNovelWordCount.value.count;

      // 构建简单的生成提示词
      final prompt = '''
请创作一篇${totalWordCount}字的短篇小说：

标题：${title.value}
类型：${selectedGenres.join('、')}
写作风格：${style.value}
背景设定：${background.value}
其他要求：${otherRequirements.value}

要求：
1. 字数控制在${totalWordCount}字左右
2. 情节完整，有开头、发展、高潮、结局
3. 语言生动，符合选定的写作风格
4. 直接输出小说内容，不要添加标题或说明

请开始创作：
''';

      // 使用流式输出生成内容
      final contentBuffer = StringBuffer();
      await for (final chunk
          in _aiService.generateShortNovelContentStream(prompt)) {
        contentBuffer.write(chunk);
        _updateRealtimeOutput(chunk);
      }

      // 保存生成的内容
      shortNovelGeneratedContent.value = contentBuffer.toString();

      // 简化保存：直接保存为单章小说
      await _saveShortNovelSimple();

      _updateRealtimeOutput(
          '\n短篇小说生成完成！总字数: ${shortNovelGeneratedContent.value.length}字\n');
    } catch (e) {
      print('[ERROR] 生成短篇小说失败: $e');
      throw Exception('生成短篇小说失败: $e');
    }
  }

  // 简化的保存方法
  Future<void> _saveShortNovelSimple() async {
    try {
      final novel = Novel(
        title: title.value,
        genre: selectedGenres.join(','),
        outline: '短篇小说，字数：${shortNovelWordCount.value.count}字',
        content: shortNovelGeneratedContent.value,
        chapters: [
          Chapter(
            number: 1,
            title: title.value,
            content: shortNovelGeneratedContent.value,
          )
        ],
        createdAt: DateTime.now(),
        style: style.value,
        sessionId: _currentSessionId.value,
      );

      await saveNovel(novel);
      _updateRealtimeOutput('短篇小说已保存\n');
    } catch (e) {
      print('[ERROR] 保存短篇小说失败: $e');
      throw Exception('保存短篇小说失败: $e');
    }
  }

  // 第一步：生成世界观和角色发展脉络
  Future<void> _generateShortNovelWorldBuilding() async {
    generationStage.value = GenerationStage.generatingShortNovelWorldBuilding;
    _updateRealtimeOutput('正在构建世界观和角色发展脉络...\n');
    generationStatus.value = '正在构建世界观和角色发展脉络...';

    try {
      // 使用新的AI服务方法生成世界观
      final worldBuildingBuffer = StringBuffer();
      await for (final chunk
          in _aiService.generateShortNovelWorldBuildingStream(
        title: title.value,
        genres: selectedGenres,
        theme: background.value,
        targetReaders: targetReader.value,
        background: background.value,
        otherRequirements: otherRequirements.value,
        wordCount: shortNovelWordCount.value.count,
        characterCards: selectedCharacterCards,
        characterTypes: selectedCharacterTypes,
      )) {
        worldBuildingBuffer.write(chunk);
        _updateRealtimeOutput(chunk);
      }

      // 保存世界观内容到NovelMemory和本地变量
      final sessionId = _getOrCreateSessionId(title.value);
      final novelMemory =
          NovelMemory(novelTitle: title.value, sessionId: sessionId);
      final worldBuildingContent = worldBuildingBuffer.toString();
      await novelMemory.saveShortNovelWorldBuilding(worldBuildingContent);

      // 保存到本地变量供预览使用
      shortNovelWorldBuilding.value = worldBuildingContent;

      _updateRealtimeOutput('\n========== 世界观构建完成 ==========\n');
      _updateRealtimeOutput('\n================================\n\n');

      generationStatus.value = '世界观构建完成';
    } catch (e) {
      print('生成世界观时出错: $e');
      throw Exception('生成世界观失败: $e');
    }
  }

  // 构建世界观生成提示词
  String _buildWorldBuildingPrompt() {
    final buffer = StringBuffer();

    // 基础信息
    buffer.writeln('请为以下短篇小说构建完整的世界观和角色发展脉络：');
    buffer.writeln('');
    buffer.writeln('小说标题：${title.value}');
    buffer.writeln('小说类型：${selectedGenres.join('、')}');
    buffer.writeln('目标字数：${shortNovelWordCount.value.count}字');
    buffer.writeln('写作风格：${style.value}');

    if (background.value.isNotEmpty) {
      buffer.writeln('背景设定：${background.value}');
    }

    if (theme.value.isNotEmpty) {
      buffer.writeln('主题内容：${theme.value}');
    }

    if (targetReaders.value.isNotEmpty) {
      buffer.writeln('目标读者：${targetReaders.value}');
    }

    // 角色信息
    if (selectedCharacterCards.isNotEmpty) {
      buffer.writeln('');
      buffer.writeln('角色设定：');
      selectedCharacterCards.forEach((typeId, cards) {
        final type = selectedCharacterTypes.firstWhere((t) => t.id == typeId);
        for (final card in cards) {
          buffer.writeln('${type.name}：${card.name}');
          if (card.gender?.isNotEmpty == true)
            buffer.writeln('  性别：${card.gender}');
          if (card.age?.isNotEmpty == true) buffer.writeln('  年龄：${card.age}');
          if (card.personalityTraits?.isNotEmpty == true)
            buffer.writeln('  性格：${card.personalityTraits}');
          if (card.background?.isNotEmpty == true)
            buffer.writeln('  背景：${card.background}');
          if (card.bodyDescription?.isNotEmpty == true)
            buffer.writeln('  外貌：${card.bodyDescription}');
          // 技能字段暂时注释，因为CharacterCard模型中没有skills字段
          // if (card.skills?.isNotEmpty == true) buffer.writeln('  技能：${card.skills}');
          buffer.writeln('');
        }
      });
    }

    // 特殊要求
    if (specialRequirements.isNotEmpty) {
      buffer.writeln('特殊要求：');
      for (final req in specialRequirements) {
        buffer.writeln('- $req');
      }
      buffer.writeln('');
    }

    buffer.writeln('''
请按以下格式输出：

## 世界观设定
[详细描述故事发生的世界背景、时代背景、社会环境等]

## 主要角色发展脉络
[详细描述每个主要角色在故事中的发展轨迹、性格弧线、关系变化等]

## 角色关系网络
[描述角色之间的关系、互动模式、冲突点等]

## 核心冲突设定
[描述故事的主要冲突、矛盾点、推动情节发展的关键因素]

## 情感基调
[描述整个故事的情感氛围、基调、想要传达的情感]

要求：
1. 世界观要与选定的类型和风格相符
2. 角色发展要有清晰的弧线和成长轨迹
3. 关系网络要合理且能推动情节发展
4. 冲突设定要适合短篇小说的篇幅
5. 情感基调要统一且深刻
''');

    return buffer.toString();
  }

  // 第二步：生成详细大纲
  Future<void> _generateShortNovelDetailedOutline() async {
    generationStage.value = GenerationStage.generatingShortNovelOutline;
    _updateRealtimeOutput('正在生成详细大纲...\n');
    generationStatus.value = '正在生成详细大纲...';

    try {
      // 获取世界观内容
      String? worldBuilding;

      // 首先尝试从本地变量获取
      if (shortNovelWorldBuilding.value.isNotEmpty) {
        worldBuilding = shortNovelWorldBuilding.value;
        print('从本地变量获取世界观内容，长度: ${worldBuilding.length}');
      } else {
        // 如果本地变量为空，则从NovelMemory中获取
        final sessionId = _getOrCreateSessionId(title.value);
        final novelMemory =
            NovelMemory(novelTitle: title.value, sessionId: sessionId);
        worldBuilding = await novelMemory.getShortNovelWorldBuilding();
        print('从NovelMemory获取世界观内容，长度: ${worldBuilding?.length ?? 0}');
      }

      if (worldBuilding == null || worldBuilding.isEmpty) {
        throw Exception('世界观内容为空，无法生成大纲');
      }

      // 使用新的AI服务方法生成详细大纲
      final outlineBuffer = StringBuffer();
      await for (final chunk in _aiService
          .generateShortNovelDetailedOutlineFromWorldBuildingStream(
        title: title.value,
        worldBuilding: worldBuilding,
        wordCount: shortNovelWordCount.value.count,
      )) {
        outlineBuffer.write(chunk);
        _updateRealtimeOutput(chunk);
      }

      // 保存详细大纲到NovelMemory和本地变量
      final outlineContent = outlineBuffer.toString();
      final sessionId = _getOrCreateSessionId(title.value);
      final novelMemory =
          NovelMemory(novelTitle: title.value, sessionId: sessionId);
      await novelMemory.saveShortNovelDetailedOutline(outlineContent);

      // 保存到本地变量供预览使用
      shortNovelDetailedOutline.value = outlineContent;

      _updateRealtimeOutput('\n========== 详细大纲生成完成 ==========\n');
      _updateRealtimeOutput('\n================================\n\n');

      generationStage.value = GenerationStage.shortNovelOutlineReady;
      generationStatus.value = '详细大纲生成完成';
    } catch (e) {
      print('生成详细大纲时出错: $e');
      throw Exception('生成详细大纲失败: $e');
    }
  }

  // 第三步：分段生成短篇小说内容
  Future<void> _generateShortNovelContent() async {
    generationStage.value = GenerationStage.generatingShortNovelContent;
    _updateRealtimeOutput('正在生成小说内容...\n');
    generationStatus.value = '正在生成小说内容...';

    try {
      // 获取世界观和详细大纲
      final sessionId = _getOrCreateSessionId(title.value);
      final novelMemory =
          NovelMemory(novelTitle: title.value, sessionId: sessionId);
      final worldBuilding = await novelMemory.getShortNovelWorldBuilding();
      final detailedOutline = await novelMemory.getShortNovelDetailedOutline();

      if (worldBuilding == null || worldBuilding.isEmpty) {
        throw Exception('世界观内容为空，无法生成小说内容');
      }
      if (detailedOutline == null || detailedOutline.isEmpty) {
        throw Exception('详细大纲为空，无法生成小说内容');
      }

      // 根据字数确定分段数量（与大纲生成逻辑保持一致）
      final totalWordCount = shortNovelWordCount.value.count;
      int segmentCount;
      if (totalWordCount <= 3000) {
        segmentCount = 2; // 开端、结局
      } else if (totalWordCount <= 5000) {
        segmentCount = 3; // 开端、发展、结局
      } else if (totalWordCount <= 8000) {
        segmentCount = 3; // 开端、发展、结局
      } else if (totalWordCount <= 15000) {
        segmentCount = 4; // 开端、发展1、发展2、结局
      } else if (totalWordCount <= 20000) {
        segmentCount = 5; // 开端、发展1、发展2、高潮、结局
      } else {
        segmentCount = 6; // 开端、发展1、发展2、发展3、高潮、结局
      }

      final segmentWordCount = (totalWordCount / segmentCount).round();
      final contentBuffer = StringBuffer();

      // 分段生成内容
      for (int i = 0; i < segmentCount; i++) {
        _updateRealtimeOutput('正在生成第${i + 1}段内容（共$segmentCount段）...\n');

        // 构建当前段落的生成提示词
        final segmentPrompt = _buildShortNovelSegmentPrompt(
          worldBuilding: worldBuilding,
          detailedOutline: detailedOutline,
          previousContent: contentBuffer.toString(),
          segmentIndex: i,
          totalSegments: segmentCount,
          targetWords: segmentWordCount,
        );

        // 使用langchain生成内容，确保连贯性
        final segmentContent = await _generateShortNovelSegmentWithMemory(
          prompt: segmentPrompt,
          worldBuilding: worldBuilding,
          detailedOutline: detailedOutline,
          previousContent: contentBuffer.toString(),
        );

        contentBuffer.write(segmentContent);
        _updateRealtimeOutput('\n第${i + 1}段生成完成\n\n');
      }

      // 保存完整的小说内容
      await _saveShortNovelToChapters(
        worldBuilding: worldBuilding,
        detailedOutline: detailedOutline,
        content: contentBuffer.toString(),
      );

      _updateRealtimeOutput('\n========== 小说内容生成完成 ==========\n');
      generationStatus.value = '小说内容生成完成';
    } catch (e) {
      print('生成小说内容时出错: $e');
      throw Exception('生成小说内容失败: $e');
    }
  }

  // 构建短篇小说段落生成提示词
  String _buildShortNovelSegmentPrompt({
    required String worldBuilding,
    required String detailedOutline,
    required String previousContent,
    required int segmentIndex,
    required int totalSegments,
    required int targetWords,
  }) {
    final buffer = StringBuffer();

    buffer.writeln('你是一位专业的短篇小说作家。请根据以下信息生成小说的第${segmentIndex + 1}段内容：');
    buffer.writeln('');
    buffer.writeln('# 世界观和角色设定');
    buffer.writeln(worldBuilding);
    buffer.writeln('');
    buffer.writeln('# 详细大纲');
    buffer.writeln(detailedOutline);
    buffer.writeln('');

    if (previousContent.isNotEmpty) {
      // 只保留最后1000字作为上下文
      final contextLength = 1000;
      final context = previousContent.length > contextLength
          ? previousContent.substring(previousContent.length - contextLength)
          : previousContent;

      buffer.writeln('# 前文内容（用于保持连贯性）');
      buffer.writeln(context);
      buffer.writeln('');
    }

    buffer.writeln('# 生成要求');
    buffer.writeln('- 这是第${segmentIndex + 1}段，共$totalSegments段');
    buffer.writeln(
        '- **重要：目标字数为${targetWords}字，不能少于${(targetWords * 0.8).round()}字！**');
    buffer.writeln('- 如果内容不够丰富，请增加更多细节描写、人物对话、心理活动、环境描写');
    buffer.writeln('- 必须严格按照世界观设定和详细大纲进行创作');

    // 根据分段数量和当前段落位置提供更精确的指导
    if (segmentIndex == 0) {
      buffer.writeln('- 这是开头部分，需要引入主要角色和背景，建立故事基调');
    } else if (segmentIndex == totalSegments - 1) {
      buffer.writeln('- 这是结尾部分，需要解决冲突并给出完整结局');
    } else {
      // 中间部分的处理
      if (totalSegments == 3) {
        buffer.writeln('- 这是发展部分，需要推进情节，展开冲突');
      } else if (totalSegments == 4) {
        if (segmentIndex == 1) {
          buffer.writeln('- 这是发展1部分，需要推进情节，逐步展开冲突');
        } else if (segmentIndex == 2) {
          buffer.writeln('- 这是发展2部分，需要将冲突推向高潮');
        }
      } else if (totalSegments == 5) {
        if (segmentIndex == 1) {
          buffer.writeln('- 这是发展1部分，需要推进情节，建立冲突');
        } else if (segmentIndex == 2) {
          buffer.writeln('- 这是发展2部分，需要深化冲突，增加复杂性');
        } else if (segmentIndex == 3) {
          buffer.writeln('- 这是高潮部分，需要将冲突推向顶点');
        }
      } else if (totalSegments == 6) {
        if (segmentIndex == 1) {
          buffer.writeln('- 这是发展1部分，需要推进情节，建立冲突');
        } else if (segmentIndex == 2) {
          buffer.writeln('- 这是发展2部分，需要深化冲突，增加复杂性');
        } else if (segmentIndex == 3) {
          buffer.writeln('- 这是发展3部分，需要进一步推进情节，为高潮做准备');
        } else if (segmentIndex == 4) {
          buffer.writeln('- 这是高潮部分，需要将冲突推向顶点，展现最激烈的对抗');
        }
      }
    }

    if (previousContent.isNotEmpty) {
      buffer.writeln('- 必须与前文内容自然衔接，保持故事连贯性');
    }

    buffer.writeln('- 语言生动有趣，符合短篇小说的特点');
    buffer.writeln('- 直接输出小说内容，不要添加标题或说明');
    buffer.writeln('');
    buffer.writeln('请开始创作：');

    return buffer.toString();
  }

  // 使用langchain memory生成短篇小说段落
  Future<String> _generateShortNovelSegmentWithMemory({
    required String prompt,
    required String worldBuilding,
    required String detailedOutline,
    required String previousContent,
  }) async {
    try {
      // 使用轻量级生成服务，利用langchain memory
      final sessionId = _getOrCreateSessionId(title.value);

      // 构建完整的上下文信息
      final fullContext = '''
# 世界观和角色设定
$worldBuilding

# 详细大纲
$detailedOutline

${previousContent.isNotEmpty ? '# 前文内容\n$previousContent\n' : ''}
''';

      final contentBuffer = StringBuffer();

      // 使用流式生成，实时显示进度
      await for (final chunk
          in _aiService.generateShortNovelContentStream(prompt)) {
        contentBuffer.write(chunk);
        _updateRealtimeOutput(chunk);
      }

      return contentBuffer.toString();
    } catch (e) {
      print('使用memory生成段落时出错: $e');
      throw Exception('生成段落失败: $e');
    }
  }

  // 保存短篇小说到章节结构
  Future<void> _saveShortNovelToChapters({
    required String worldBuilding,
    required String detailedOutline,
    required String content,
  }) async {
    try {
      // 创建第0章：存储世界观和大纲
      final chapter0Content = '''
# 世界观设定

$worldBuilding

# 详细大纲

$detailedOutline
''';

      final chapter0 = Chapter(
        number: 0,
        title: '世界观与大纲',
        content: chapter0Content,
      );

      // 创建第1章：存储小说内容
      final chapter1 = Chapter(
        number: 1,
        title: title.value,
        content: content,
      );

      // 创建小说对象
      final novel = Novel(
        title: title.value,
        genre: selectedGenres.join('、'),
        outline: detailedOutline,
        content: content,
        chapters: [chapter0, chapter1],
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // 保存到数据库
      await saveNovel(novel);
      _updateRealtimeOutput('短篇小说已保存到我的书库\n');
    } catch (e) {
      print('保存短篇小说时出错: $e');
      throw Exception('保存短篇小说失败: $e');
    }
  }

  // 构建知识库提示词
  String _buildKnowledgeBasePrompt() {
    try {
      final knowledgeBaseController = Get.find<KnowledgeBaseController>();

      if (!knowledgeBaseController.useKnowledgeBase.value ||
          knowledgeBaseController.selectedDocIds.isEmpty) {
        return '';
      }

      final knowledgeContent = knowledgeBaseController.getSelectedDocsContent();
      if (knowledgeContent.isEmpty) {
        return '';
      }

      return '''
# 专业知识库
请将以下专业知识融入创作中，确保内容的专业性和准确性：

$knowledgeContent

请在创作时自然地运用这些知识，不要生硬地堆砌信息。
''';
    } catch (e) {
      print('获取知识库内容失败: $e');
      return '';
    }
  }

  /// 构建续写模式的优化查询文本
  ///
  /// 专门为续写场景优化，重点关注连贯性和一致性
  String _buildContinuationQueryText({
    required String novelTitle,
    required int chapterNumber,
    required String chapterTitle,
    required String chapterOutline,
    required List<String> genres,
    required String theme,
  }) {
    final buffer = StringBuffer();

    // 1. 明确这是续写任务
    buffer.write('续写小说《$novelTitle》第$chapterNumber章《$chapterTitle》');

    // 2. 添加类型和主题信息
    if (genres.isNotEmpty) {
      buffer.write(' 类型：${genres.join('、')}');
    }
    if (theme.isNotEmpty) {
      buffer.write(' 主题：$theme');
    }

    // 3. 添加章节大纲
    buffer.write(' 章节大纲：$chapterOutline');

    // 4. 强调前文故事情节和人物发展的续写检索需求
    buffer.write(' 续写重点检索前文：');

    // 4.1 前文故事情节深度检索
    buffer.write('关键情节节点和转折、未解决的故事线索、');
    buffer.write('重要事件的前因后果、情节发展的逻辑链条、');
    buffer.write('埋下的伏笔和悬念、故事节奏和氛围、');

    // 4.2 人物发展深度检索
    buffer.write('主要角色的完整发展轨迹、性格成长变化过程、');
    buffer.write('人物关系的演变历史、角色动机和目标变化、');
    buffer.write('重要的人物互动和对话、情感发展脉络、');
    buffer.write('角色的行为模式和思维方式、');

    // 4.3 连贯性关键要素
    buffer.write('角色当前的身心状态、未完成的行动和对话、');
    buffer.write('环境和场景的延续、情感氛围的承接');

    // 5. 根据章节位置添加特定的续写重点
    if (chapterNumber <= 3) {
      buffer.write(' 前期续写重点：保持开篇风格、延续人物设定、维持世界观一致性、');
      buffer.write('前文人物介绍的细节、初始人物关系、开篇氛围的延续');
    } else if (chapterNumber <= 10) {
      buffer.write(' 中期续写重点：推进主线情节、深化人物关系、处理已有冲突、');
      buffer.write('前文重要情节的发展、人物成长的轨迹、冲突升级的过程');
    } else {
      buffer.write(' 后期续写重点：解决核心冲突、呼应前文伏笔、准备故事收尾、');
      buffer.write('前文所有重要线索、人物发展的完整历程、需要解决的所有问题');
    }

    // 6. 根据章节大纲内容添加智能化的续写检索重点
    final intelligentFocus =
        _analyzeContinuationOutlineForQueryFocus(chapterOutline);
    if (intelligentFocus.isNotEmpty) {
      buffer.write(' 智能续写检索重点：$intelligentFocus');
    }

    // 7. 添加续写连贯性目标
    buffer.write(' 续写目标：确保与前文在情节发展、人物行为、情感状态、世界观设定上完全连贯');

    return buffer.toString();
  }

  /// 构建续写大纲的优化查询文本
  ///
  /// 专门为续写大纲生成优化，重点关注整体故事结构和发展方向
  String _buildOutlineContinuationQueryText({
    required String novelTitle,
    required int startChapter,
    required int endChapter,
    required List<String> genres,
    required String theme,
    required String targetReader,
  }) {
    final buffer = StringBuffer();

    // 1. 明确这是续写大纲任务
    buffer.write('续写小说《$novelTitle》大纲 从第$startChapter章到第$endChapter章');

    // 2. 添加类型和主题信息
    if (genres.isNotEmpty) {
      buffer.write(' 类型：${genres.join('、')}');
    }
    if (theme.isNotEmpty) {
      buffer.write(' 主题：$theme');
    }
    if (targetReader.isNotEmpty) {
      buffer.write(' 目标读者：$targetReader');
    }

    // 3. 强调前文故事情节和人物发展的续写大纲检索需求
    buffer.write(' 续写大纲重点检索前文：');

    // 3.1 故事情节结构分析
    buffer.write('已完成的主要情节线、故事发展的整体脉络、');
    buffer.write('重要事件的影响和后续、未解决的核心冲突、');
    buffer.write('故事节奏和张力变化、关键转折点的设置、');

    // 3.2 人物发展轨迹分析
    buffer.write('主要角色的成长历程、人物关系网络的演变、');
    buffer.write('角色目标和动机的变化、重要的人物互动模式、');
    buffer.write('角色性格的发展方向、情感线的发展状态、');

    // 3.3 整体结构规划需求
    buffer.write('故事主题的深化方向、冲突升级的可能路径、');
    buffer.write('高潮设计的铺垫要素、结局走向的伏笔线索');

    // 4. 根据章节范围添加特定的续写重点
    final totalChapters = endChapter - startChapter + 1;
    if (totalChapters <= 5) {
      buffer.write(' 续写重点：短期情节发展、局部冲突解决、人物关系推进');
    } else if (totalChapters <= 15) {
      buffer.write(' 续写重点：中期故事发展、主要冲突升级、关键转折点设计');
    } else {
      buffer.write(' 续写重点：长期故事规划、多线索整合、高潮与结局设计');
    }

    // 5. 添加结构化要求
    buffer.write(' 结构要求：保持节奏平衡、确保逻辑连贯、维持角色一致性');

    return buffer.toString();
  }

  /// 分析续写章节大纲内容，提取智能化的检索重点
  ///
  /// 专门为续写场景优化，重点关注与前文的连接点
  String _analyzeContinuationOutlineForQueryFocus(String chapterOutline) {
    final lowerOutline = chapterOutline.toLowerCase();
    final focusPoints = <String>[];

    // 检测人物互动相关关键词
    final interactionKeywords = [
      '见面',
      '相遇',
      '对话',
      '交流',
      '争吵',
      '和解',
      '分别',
      '重逢'
    ];
    if (interactionKeywords.any((keyword) => lowerOutline.contains(keyword))) {
      focusPoints.add('相关人物的前文互动历史、关系发展状态、未解决的人际问题');
    }

    // 检测情感发展相关关键词
    final emotionKeywords = ['爱情', '友情', '亲情', '信任', '背叛', '原谅', '误解', '理解'];
    if (emotionKeywords.any((keyword) => lowerOutline.contains(keyword))) {
      focusPoints.add('相关角色的情感发展历程、情感状态变化、重要的情感转折点');
    }

    // 检测冲突解决相关关键词
    final resolutionKeywords = ['解决', '处理', '面对', '克服', '战胜', '失败', '成功', '结果'];
    if (resolutionKeywords.any((keyword) => lowerOutline.contains(keyword))) {
      focusPoints.add('前文相关冲突的起源和发展、角色的应对方式、之前的尝试和结果');
    }

    // 检测回忆/揭示相关关键词
    final revelationKeywords = ['回忆', '想起', '发现', '揭示', '真相', '秘密', '过去', '隐瞒'];
    if (revelationKeywords.any((keyword) => lowerOutline.contains(keyword))) {
      focusPoints.add('相关的历史事件、隐藏的信息、角色的过往经历、重要的背景故事');
    }

    // 检测决定/行动相关关键词
    final actionKeywords = ['决定', '选择', '行动', '计划', '准备', '出发', '前往', '开始'];
    if (actionKeywords.any((keyword) => lowerOutline.contains(keyword))) {
      focusPoints.add('角色的思考过程、价值观念、影响决策的前文事件、相关的动机发展');
    }

    // 检测转折/变化相关关键词
    final changeKeywords = ['转折', '变化', '改变', '突然', '意外', '惊讶', '震惊', '不同'];
    if (changeKeywords.any((keyword) => lowerOutline.contains(keyword))) {
      focusPoints.add('前文的铺垫和伏笔、角色的预期和计划、可能的转折点准备');
    }

    // 检测成长/发展相关关键词
    final growthKeywords = ['成长', '学会', '明白', '领悟', '进步', '提升', '突破', '觉醒'];
    if (growthKeywords.any((keyword) => lowerOutline.contains(keyword))) {
      focusPoints.add('角色的成长轨迹、学习过程、重要的启发时刻、性格发展历程');
    }

    return focusPoints.join('、');
  }
}
